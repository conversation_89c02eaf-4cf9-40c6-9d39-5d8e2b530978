@use '@theme/functions.scss' as *;

$mobile-breakpoint: 768px;
$tablet-breakpoint: 1024px;
$desktop-breakpoint: 1200px;

.footer {
  background-color: var(--color-background-secondary, #1a1a1a);
  color: var(--color-text-primary, #ffffff);
  padding: calculate-rem(48px) 0;
  margin-top: auto;
}

.container {
  max-width: calculate-rem(1200px);
  margin: 0 auto;
  padding: 0 calculate-rem(24px);
}

.content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--footer-gap, calculate-rem(48px));
  margin-bottom: calculate-rem(32px);

  @media (max-width: $mobile-breakpoint) {
    flex-direction: column;
    gap: calculate-rem(32px);
  }
}

.leftSection {
  display: flex;
  flex-direction: column;
  gap: calculate-rem(24px);
  min-width: calculate-rem(200px);
}

.centerSection {
  display: flex;
  flex: 1;
  justify-content: center;
}

.rightSection {
  display: flex;
  flex-direction: column;
  gap: calculate-rem(24px);
  min-width: calculate-rem(200px);
}

.fullWidthSection {
  width: 100%;
}
