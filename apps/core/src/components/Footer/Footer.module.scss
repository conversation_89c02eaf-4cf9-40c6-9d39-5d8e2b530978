@use '@theme/functions.scss' as *;
@use '@theme/variables.scss' as *;

$mobile-breakpoint: 768px;
$tablet-breakpoint: 1024px;
$desktop-breakpoint: 1200px;

.footer {
  background-color: linear-gradient(80deg, $color-surface-100, $color-surface-100);
  color: $color-text-primary;
  padding: calculate-rem(48px) 0;
  margin-top: auto;
}

.container {
  max-width: calculate-rem(1200px);
  margin: 0 auto;
  padding: 0 calculate-rem(24px);
}

.content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--footer-gap, calculate-rem(48px));
  margin-bottom: calculate-rem(32px);

  @media (max-width: $mobile-breakpoint) {
    flex-direction: column;
    gap: calculate-rem(32px);
  }
}

.leftSection {
  display: flex;
  flex-direction: column;
  gap: calculate-rem(24px);
  min-width: calculate-rem(200px);
}

.centerSection {
  display: flex;
  flex: 1;
  justify-content: center;
}

.rightSection {
  display: flex;
  flex-direction: column;
  gap: calculate-rem(24px);
  min-width: calculate-rem(200px);
}

.fullWidthSection {
  width: 100%;
}

.logoSection {
  display: flex;
  flex-direction: column;
  gap: calculate-rem(16px);
}

.logoContainer {
  display: flex;
  align-items: center;
}

.logoLink {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: inherit;

  &:hover {
    opacity: 0.8;
  }
}

.logoText {
  font-size: calculate-rem(24px);
  font-weight: 700;
  letter-spacing: calculate-rem(1px);
  color: $color-text-primary;
}

.logoSuperscript {
  font-size: calculate-rem(12px);
  margin-left: calculate-rem(4px);
  color: $color-text-primary;
}

.socialMedia {
  display: flex;
  gap: calculate-rem(12px);
  flex-wrap: wrap;
}

.socialLink {
  display: flex;
  align-items: center;
  justify-content: center;
  width: calculate-rem(40px);
  height: calculate-rem(40px);
  border-radius: calculate-rem(8px);
  background-color: $color-surface-100;
  color: $color-text-primary;
  text-decoration: none;
  transition: all 0.2s ease;

  &:hover {
    background-color: $color-surface-200;
    transform: translateY(calculate-rem(-2px));
  }

  &:active {
    transform: translateY(0);
  }
}

.navigationArea {
  display: flex;
  gap: calculate-rem(48px);
  flex: 1;
  width: 100%;

  @media (max-width: $tablet-breakpoint) {
    gap: calculate-rem(32px);
  }

  @media (max-width: $mobile-breakpoint) {
    flex-direction: column;
    gap: calculate-rem(24px);
  }
}

.navigationSection {
  display: flex;
  flex-direction: column;
  gap: calculate-rem(16px);
  min-width: calculate-rem(150px);
  flex: 1;
}

.sectionTitle {
  font-size: calculate-rem(16px);
  font-weight: 600;
  color: $color-text-primary;
  margin: 0;
  margin-bottom: calculate-rem(8px);
}

.linkList {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: calculate-rem(8px);
}

.footerLink {
  font-size: calculate-rem(14px);
  color: $color-text-secondary;
  text-decoration: none;
  line-height: 1.5;
  transition: color 0.2s ease;

  &:hover {
    color: $color-text-primary;
  }

  &:focus {
    outline: 2px solid $color-primary;
    outline-offset: 2px;
    border-radius: calculate-rem(4px);
  }
}

.disclaimerSection {
  border-top: 1px solid $color-border;
  padding-top: calculate-rem(24px);
  display: flex;
  flex-direction: column;
  gap: calculate-rem(16px);
}

.responsibleGambling {
  display: flex;
  flex-direction: column;
  gap: calculate-rem(12px);

  @media (min-width: $mobile-breakpoint) {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
}

.rgMessage {
  font-size: calculate-rem(14px);
  font-weight: 600;
  color: $color-text-primary;
  margin: 0;
}

.rgLinks {
  display: flex;
  flex-wrap: wrap;
  gap: calculate-rem(16px);

  @media (max-width: $mobile-breakpoint) {
    gap: calculate-rem(12px);
  }
}

.rgLink {
  font-size: calculate-rem(12px);
  color: $color-text-secondary;
  text-decoration: underline;
  transition: color 0.2s ease;

  &:hover {
    color: $color-text-primary;
  }

  &:focus {
    outline: 2px solid $color-primary;
    outline-offset: 2px;
    border-radius: calculate-rem(4px);
  }
}

.disclaimer {
  display: flex;
  flex-direction: column;
  gap: calculate-rem(12px);
}

.disclaimerText {
  font-size: calculate-rem(12px);
  color: $color-text-secondary;
  line-height: 1.5;
  margin: 0;
}

.companyInfo {
  display: flex;
  flex-direction: column;
  gap: calculate-rem(8px);
}

.companyText {
  font-size: calculate-rem(11px);
  color: $color-text-secondary;
  line-height: 1.4;
  margin: 0;
}

// Responsive Design
@media (max-width: $mobile-breakpoint) {
  .footer {
    padding: calculate-rem(32px) 0;
  }

  .container {
    padding: 0 calculate-rem(16px);
  }

  .content {
    margin-bottom: calculate-rem(24px);
  }

  .leftSection {
    align-items: center;
    text-align: center;
  }

  .socialMedia {
    justify-content: center;
  }

  .navigationArea {
    width: 100%;
  }

  .navigationSection {
    text-align: center;
  }

  .responsibleGambling {
    text-align: center;
  }

  .rgLinks {
    justify-content: center;
  }
}
