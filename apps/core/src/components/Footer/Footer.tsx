import type { FC } from 'react'
import React from 'react'
import { Facebook, Twitter, Twitch, MessageCircle, Send, Instagram, Youtube, Video } from 'lucide-react'
import { LayoutConfigService } from '@/services/LayoutConfig.service'
import type {
  BrandSectionConfig,
  NavigationSectionConfig,
  SocialMediaSectionConfig,
  LegalSectionConfig,
} from '@repo/types/api/s3/layout-config'
import styles from './Footer.module.scss'

interface FooterProps {
  market: string
}

const getSocialIcon = (iconName: string) => {
  const iconMap = {
    facebook: Facebook,
    twitter: Twitter,
    twitch: Twitch,
    'message-circle': MessageCircle,
    send: Send,
    instagram: Instagram,
    youtube: Youtube,
    video: Video, // For TikTok
  }
  return iconMap[iconName as keyof typeof iconMap] || MessageCircle
}

// Section Components
const BrandSection: FC<{ config: BrandSectionConfig }> = ({ config }) => (
  <div className={styles.logoSection}>
    <div className={styles.logoContainer}>
      <a
        href={config.logo.href}
        className={styles.logoLink}
        target={config.logo.target}
        aria-label={`${config.logo.text} - Return to Homepage`}>
        <span className={styles.logoText}>{config.logo.text}</span>
        {config.logo.superscript && <sup className={styles.logoSuperscript}>{config.logo.superscript}</sup>}
      </a>
    </div>
  </div>
)

const SocialMediaSection: FC<{ config: SocialMediaSectionConfig }> = ({ config }) => (
  <div className={styles.socialMedia}>
    {config.platforms
      .filter(platform => platform.enabled)
      .map(social => {
        const IconComponent = getSocialIcon(social.icon)
        return (
          <a
            key={social.id}
            href={social.url}
            target={social.target}
            rel="noopener noreferrer"
            className={styles.socialLink}
            aria-label={social.ariaLabel || `Visit our ${social.platform} page`}>
            <IconComponent size={20} />
          </a>
        )
      })}
  </div>
)

const NavigationSection: FC<{ config: NavigationSectionConfig }> = ({ config }) => (
  <div className={styles.navigationArea}>
    {config.columns
      .filter(column => column.enabled)
      .map(column => (
        <div key={column.id} className={styles.navigationSection}>
          <h3 className={styles.sectionTitle}>{column.title}</h3>
          <ul className={styles.linkList}>
            {column.links
              .filter(link => link.enabled)
              .map(link => (
                <li key={link.id}>
                  <a
                    href={link.url}
                    target={link.target}
                    rel={link.target === '_blank' ? 'noopener noreferrer' : undefined}
                    className={styles.footerLink}>
                    {link.title}
                  </a>
                </li>
              ))}
          </ul>
        </div>
      ))}
  </div>
)

const LegalSection: FC<{ config: LegalSectionConfig }> = ({ config }) => (
  <div className={styles.disclaimerSection}>
    {/* Responsible Gambling */}
    {config.responsibleGambling.enabled && (
      <div className={styles.responsibleGambling}>
        <p className={styles.rgMessage}>{config.responsibleGambling.message}</p>
        <div className={styles.rgLinks}>
          {config.responsibleGambling.links
            .filter(link => link.enabled)
            .map(link => (
              <a
                key={link.id}
                href={link.url}
                target={link.target}
                rel={link.target === '_blank' ? 'noopener noreferrer' : undefined}
                className={styles.rgLink}>
                {link.title}
              </a>
            ))}
        </div>
      </div>
    )}

    {/* Disclaimer */}
    {config.disclaimer.enabled && (
      <div className={styles.disclaimer}>
        <p className={styles.disclaimerText}>{config.disclaimer.text}</p>
        {config.companyInfo.enabled && (
          <div className={styles.companyInfo}>
            <p className={styles.companyText}>{config.companyInfo.copyright}</p>
            <p className={styles.companyText}>
              {config.companyInfo.name} - {config.companyInfo.address}
            </p>
            <p className={styles.companyText}>{config.companyInfo.license}</p>
          </div>
        )}
      </div>
    )}
  </div>
)

export const Footer: FC<FooterProps> = async ({ market }) => {
  let layoutConfig = null

  try {
    layoutConfig = await LayoutConfigService.getFooterLayoutConfig(market)
  } catch (error) {
    console.error('Failed to fetch footer layout config:', error)
    return null
  }

  if (!layoutConfig) {
    return null
  }

  // Validate configuration
  const validation = LayoutConfigService.validateConfig(layoutConfig)
  if (!validation.isValid) {
    console.error('Invalid footer layout config:', validation.errors)
    return null
  }

  // Get sections by position using the layout configuration
  const leftSections = LayoutConfigService.getSectionsByPosition(layoutConfig, 'left')
  const centerSections = LayoutConfigService.getSectionsByPosition(layoutConfig, 'center')
  const rightSections = LayoutConfigService.getSectionsByPosition(layoutConfig, 'right')
  const fullWidthSections = LayoutConfigService.getSectionsByPosition(layoutConfig, 'full-width')

  // Generate dynamic styles based on layout configuration
  const layoutStyles = {
    '--footer-padding': layoutConfig.layout.spacing?.padding || '48px 0',
    '--footer-gap': layoutConfig.layout.spacing?.gap || '32px',
    '--footer-margin': layoutConfig.layout.spacing?.margin || '0',
    '--mobile-breakpoint': `${layoutConfig.layout.breakpoints?.mobile || 768}px`,
    '--tablet-breakpoint': `${layoutConfig.layout.breakpoints?.tablet || 1024}px`,
    '--desktop-breakpoint': `${layoutConfig.layout.breakpoints?.desktop || 1200}px`,
  } as React.CSSProperties

  // Get responsive configuration for current breakpoint (you could detect this with useMediaQuery)
  const getResponsiveSection = (section: any, breakpoint: 'mobile' | 'tablet' | 'desktop' = 'desktop') => {
    return LayoutConfigService.getResponsiveConfig(section, breakpoint)
  }

  // Render sections dynamically based on position with responsive support
  const renderSectionsByPosition = (sections: any[], className: string) => {
    if (sections.length === 0) return null

    return (
      <div className={className}>
        {sections.map((section) => {
          // Apply responsive configuration (in a real app, you'd detect the current breakpoint)
          const responsiveSection = getResponsiveSection(section, 'desktop')

          switch (responsiveSection.type) {
            case 'brand':
              return <BrandSection key={responsiveSection.id} config={responsiveSection.config} />
            case 'social-media':
              return <SocialMediaSection key={responsiveSection.id} config={responsiveSection.config} />
            case 'navigation':
              return <NavigationSection key={responsiveSection.id} config={responsiveSection.config} />
            case 'legal':
              return <LegalSection key={responsiveSection.id} config={responsiveSection.config} />
            default:
              return null
          }
        })}
      </div>
    )
  }

  return (
    <footer
      className={`${styles.footer} ${styles[layoutConfig.layout.type]}`}
      style={layoutStyles}
      data-layout-type={layoutConfig.layout.type}
      data-layout-direction={layoutConfig.layout.direction}
    >
      <div className={styles.container}>
        <div
          className={styles.content}
          data-wrap={layoutConfig.layout.wrap}
        >
          {/* Left Section */}
          {renderSectionsByPosition(leftSections, styles.leftSection || '')}

          {/* Center Section */}
          {renderSectionsByPosition(centerSections, styles.centerSection || styles.navigationArea)}

          {/* Right Section */}
          {renderSectionsByPosition(rightSections, styles.rightSection || '')}
        </div>

        {/* Full Width Sections */}
        {renderSectionsByPosition(fullWidthSections, styles.fullWidthSection || styles.disclaimerSection)}
      </div>
    </footer>
  )
}
