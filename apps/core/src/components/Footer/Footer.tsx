import type { FC } from 'react'
import React from 'react'
import { Facebook, Twitter, Twitch, MessageCircle, Send, Instagram, Youtube, Video } from 'lucide-react'
import { LayoutConfigService } from '@/services/LayoutConfig.service'
import type {
  BrandSectionConfig,
  NavigationSectionConfig,
  SocialMediaSectionConfig,
  LegalSectionConfig,
} from '@repo/types/api/s3/layout-config'
import styles from './Footer.module.scss'

interface FooterProps {
  market: string
}

const getSocialIcon = (iconName: string) => {
  const iconMap = {
    facebook: Facebook,
    twitter: Twitter,
    twitch: Twitch,
    'message-circle': MessageCircle,
    send: Send,
    instagram: Instagram,
    youtube: Youtube,
    video: Video, // For TikTok
  }
  return iconMap[iconName as keyof typeof iconMap] || MessageCircle
}

// Section Components
const BrandSection: FC<{ config: BrandSectionConfig }> = ({ config }) => (
  <div className={styles.logoSection}>
    <div className={styles.logoContainer}>
      <a
        href={config.logo.href}
        className={styles.logoLink}
        target={config.logo.target}
        aria-label={`${config.logo.text} - Return to Homepage`}>
        <span className={styles.logoText}>{config.logo.text}</span>
        {config.logo.superscript && <sup className={styles.logoSuperscript}>{config.logo.superscript}</sup>}
      </a>
    </div>
  </div>
)

const SocialMediaSection: FC<{ config: SocialMediaSectionConfig }> = ({ config }) => (
  <div className={styles.socialMedia}>
    {config.platforms
      .filter(platform => platform.enabled)
      .map(social => {
        const IconComponent = getSocialIcon(social.icon)
        return (
          <a
            key={social.id}
            href={social.url}
            target={social.target}
            rel="noopener noreferrer"
            className={styles.socialLink}
            aria-label={social.ariaLabel || `Visit our ${social.platform} page`}>
            <IconComponent size={20} />
          </a>
        )
      })}
  </div>
)

const NavigationSection: FC<{ config: NavigationSectionConfig }> = ({ config }) => (
  <div className={styles.navigationArea}>
    {config.columns
      .filter(column => column.enabled)
      .map(column => (
        <div key={column.id} className={styles.navigationSection}>
          <h3 className={styles.sectionTitle}>{column.title}</h3>
          <ul className={styles.linkList}>
            {column.links
              .filter(link => link.enabled)
              .map(link => (
                <li key={link.id}>
                  <a
                    href={link.url}
                    target={link.target}
                    rel={link.target === '_blank' ? 'noopener noreferrer' : undefined}
                    className={styles.footerLink}>
                    {link.title}
                  </a>
                </li>
              ))}
          </ul>
        </div>
      ))}
  </div>
)

const LegalSection: FC<{ config: LegalSectionConfig }> = ({ config }) => (
  <div className={styles.disclaimerSection}>
    {/* Responsible Gambling */}
    {config.responsibleGambling.enabled && (
      <div className={styles.responsibleGambling}>
        <p className={styles.rgMessage}>{config.responsibleGambling.message}</p>
        <div className={styles.rgLinks}>
          {config.responsibleGambling.links
            .filter(link => link.enabled)
            .map(link => (
              <a
                key={link.id}
                href={link.url}
                target={link.target}
                rel={link.target === '_blank' ? 'noopener noreferrer' : undefined}
                className={styles.rgLink}>
                {link.title}
              </a>
            ))}
        </div>
      </div>
    )}

    {/* Disclaimer */}
    {config.disclaimer.enabled && (
      <div className={styles.disclaimer}>
        <p className={styles.disclaimerText}>{config.disclaimer.text}</p>
        {config.companyInfo.enabled && (
          <div className={styles.companyInfo}>
            <p className={styles.companyText}>{config.companyInfo.copyright}</p>
            <p className={styles.companyText}>
              {config.companyInfo.name} - {config.companyInfo.address}
            </p>
            <p className={styles.companyText}>{config.companyInfo.license}</p>
          </div>
        )}
      </div>
    )}
  </div>
)

export const Footer: FC<FooterProps> = async ({ market }) => {
  let layoutConfig = null

  try {
    layoutConfig = await LayoutConfigService.getFooterLayoutConfig(market)
  } catch (error) {
    console.error('Failed to fetch footer layout config:', error)
    return null
  }

  if (!layoutConfig) {
    return null
  }

  // Validate configuration
  const validation = LayoutConfigService.validateConfig(layoutConfig)
  if (!validation.isValid) {
    console.error('Invalid footer layout config:', validation.errors)
    return null
  }

  // Extract sections with full type safety using discriminated unions
  const brandSection = LayoutConfigService.getBrandSection(layoutConfig, 'brand-section')
  const socialSection = LayoutConfigService.getSocialMediaSection(layoutConfig, 'social-media-section')
  const navigationSection = LayoutConfigService.getNavigationSection(layoutConfig, 'navigation-section')
  const legalSection = LayoutConfigService.getLegalSection(layoutConfig, 'legal-section')

  return (
    <footer className={styles.footer}>
      <div className={styles.container}>
        <div className={styles.content}>
          {/* Left Section */}
          <div className={styles.leftSection}>
            {brandSection && <BrandSection config={brandSection.config} />}
            {socialSection && <SocialMediaSection config={socialSection.config} />}
          </div>

          {/* Center Section */}
          {navigationSection && <NavigationSection config={navigationSection.config} />}
        </div>

        {/* Full Width Section */}
        {legalSection && <LegalSection config={legalSection.config} />}
      </div>
    </footer>
  )
}
