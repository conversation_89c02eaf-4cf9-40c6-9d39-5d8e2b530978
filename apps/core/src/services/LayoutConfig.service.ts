import 'server-only'
import { unstable_cache } from 'next/cache'
import { s3F<PERSON>ch<PERSON><PERSON> } from '@/network/server-utils/s3/S3FetchApi'
import type {
  LayoutConfig,
  FooterLayoutConfig,
  LayoutSection,
  LayoutConfigValidation,
  BrandLayoutSection,
  NavigationLayoutSection,
  SocialMediaLayoutSection,
  LegalLayoutSection
} from '@repo/types/api/s3/layout-config'

/**
 * Layout Configuration Service
 * 
 * Provides a centralized service for managing layout configurations
 * across the application. Can be extended for Header, Sidebar, etc.
 */
export class LayoutConfigService {
  
  /**
   * Get footer layout configuration for a specific market
   */
  static getFooterLayoutConfig = unstable_cache(
    async (market: string): Promise<FooterLayoutConfig | null> => {
      try {
        const config = await s3FetchApi.getFooterLayoutConfig(market)
        return config
      } catch (error) {
        console.error('Failed to fetch footer layout config:', error)
        return null
      }
    },
    ['footer-layout-config'],
    { revalidate: 300 } // 5 minutes cache
  )

  /**
   * Get enabled sections from layout config
   */
  static getEnabledSections(config: LayoutConfig): LayoutSection[] {
    return config.sections
      .filter(section => section.enabled)
      .sort((a, b) => a.order - b.order)
  }

  /**
   * Get sections by position
   */
  static getSectionsByPosition(
    config: LayoutConfig, 
    position: LayoutSection['position']
  ): LayoutSection[] {
    return this.getEnabledSections(config)
      .filter(section => section.position === position)
  }

  /**
   * Get responsive configuration for current breakpoint
   */
  static getResponsiveConfig(
    section: LayoutSection,
    breakpoint: 'mobile' | 'tablet' | 'desktop'
  ): LayoutSection {
    const responsiveOverrides = section.responsive?.[breakpoint]
    if (!responsiveOverrides) return section

    return {
      ...section,
      ...responsiveOverrides
    } as LayoutSection
  }

  /**
   * Validate layout configuration
   */
  static validateConfig(config: LayoutConfig): LayoutConfigValidation {
    const errors: string[] = []
    const warnings: string[] = []

    // Required fields validation
    if (!config.id) errors.push('Config ID is required')
    if (!config.name) errors.push('Config name is required')
    if (!config.version) errors.push('Config version is required')
    if (!config.sections || config.sections.length === 0) {
      errors.push('At least one section is required')
    }

    // Section validation
    config.sections?.forEach((section, index) => {
      if (!section.id) errors.push(`Section ${index}: ID is required`)
      if (!section.type) errors.push(`Section ${index}: Type is required`)
      if (typeof section.enabled !== 'boolean') {
        errors.push(`Section ${index}: Enabled must be boolean`)
      }
      if (typeof section.order !== 'number') {
        errors.push(`Section ${index}: Order must be number`)
      }
    })

    // Duplicate section IDs
    const sectionIds = config.sections?.map(s => s.id) || []
    const duplicateIds = sectionIds.filter((id, index) => sectionIds.indexOf(id) !== index)
    if (duplicateIds.length > 0) {
      errors.push(`Duplicate section IDs: ${duplicateIds.join(', ')}`)
    }

    // Warnings for best practices
    const enabledSections = config.sections?.filter(s => s.enabled) || []
    if (enabledSections.length === 0) {
      warnings.push('No sections are enabled')
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }

  /**
   * Get section configuration with type safety using discriminated unions
   */
  static getSectionConfig<T extends LayoutSection['config']>(
    config: LayoutConfig,
    sectionId: string
  ): T | null {
    const section = config.sections.find(s => s.id === sectionId)
    return section?.config as T || null
  }

  /**
   * Get section by ID with full type safety
   */
  static getSection(
    config: LayoutConfig,
    sectionId: string
  ): LayoutSection | null {
    return config.sections.find(s => s.id === sectionId) || null
  }

  /**
   * Type-safe section getters
   */
  static getBrandSection(config: LayoutConfig, sectionId: string): BrandLayoutSection | null {
    const section = this.getSection(config, sectionId)
    return section?.type === 'brand' ? section as BrandLayoutSection : null
  }

  static getNavigationSection(config: LayoutConfig, sectionId: string): NavigationLayoutSection | null {
    const section = this.getSection(config, sectionId)
    return section?.type === 'navigation' ? section as NavigationLayoutSection : null
  }

  static getSocialMediaSection(config: LayoutConfig, sectionId: string): SocialMediaLayoutSection | null {
    const section = this.getSection(config, sectionId)
    return section?.type === 'social-media' ? section as SocialMediaLayoutSection : null
  }

  static getLegalSection(config: LayoutConfig, sectionId: string): LegalLayoutSection | null {
    const section = this.getSection(config, sectionId)
    return section?.type === 'legal' ? section as LegalLayoutSection : null
  }

  /**
   * Check if section is enabled
   */
  static isSectionEnabled(config: LayoutConfig, sectionId: string): boolean {
    const section = config.sections.find(s => s.id === sectionId)
    return section?.enabled ?? false
  }

  /**
   * Get layout metadata
   */
  static getLayoutMetadata(config: LayoutConfig) {
    return {
      id: config.id,
      name: config.name,
      version: config.version,
      market: config.market,
      lastUpdated: config.lastUpdated,
      metadata: config.metadata
    }
  }
}

// Export for easier imports
export const {
  getFooterLayoutConfig,
  getEnabledSections,
  getSectionsByPosition,
  getResponsiveConfig,
  validateConfig,
  getSectionConfig,
  isSectionEnabled,
  getLayoutMetadata
} = LayoutConfigService
