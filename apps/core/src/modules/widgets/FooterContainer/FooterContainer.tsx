import React from 'react'
import { getFooterConfig } from '@/network/server-utils/s3/getters'
import { FooterDisclaimerWidget } from '@modules/widgets/FooterDisclaimerWidget/FooterDisclaimerWidget'
import { FooterLogoWidget } from '@modules/widgets/FooterLogoWidget/FooterLogoWidget'
import { FooterNavigationWidget } from '@modules/widgets/FooterNavigationWidget/FooterNavigationWidget'
import { FooterSocialWidget } from '@modules/widgets/FooterSocialWidget/FooterSocialWidget'
import type { IDynamicComponentProps } from '@/types/components'
import type { GetFooterConfigLegacyResponse } from '@repo/types/api/s3/footer-config'
import styles from '@modules/widgets/FooterContainer/FooterContainer.module.scss'

export const FooterContainer = async ({ market }: IDynamicComponentProps) => {
  let footerConfig: GetFooterConfigLegacyResponse | null = null

  try {
    footerConfig = await getFooterConfig(market)
  } catch (error) {
    console.error('Failed to fetch footer config:', error)
  }

  if (!footerConfig) {
    return (
      <footer className={styles.footer}>
        <div className={styles.error}>
          <p>Failed to load footer configuration</p>
        </div>
      </footer>
    )
  }

  // Extract data for each widget from the footer config
  const footerItems = footerConfig.new_footer_items || footerConfig.footer_items || []
  
  const navigationSection = footerItems.find(item => item.type === 'navigation_section')
  const socialMediaSection = footerItems.find(item => item.type === 'social_media')
  const disclaimerSection = footerItems.find(item => item.type === 'disclaimer')

  // Prepare data for each widget
  const logoData = {
    name: 'LUCKY ONE',
    superscript: 'US',
    href: '/',
    imgUrl: null
  }

  const navigationData = Array.isArray(navigationSection?.items) ? navigationSection.items : []
  const socialData = Array.isArray(socialMediaSection?.items) ? socialMediaSection.items : []
  const disclaimerData = {
    text: typeof disclaimerSection?.items === 'string' ? disclaimerSection.items : '',
    responsibleGambling: {
      message: 'Play Responsibly',
      links: [
        {
          title: 'National Council on Problem Gambling',
          url: 'https://www.ncpgambling.org/'
        },
        {
          title: '1-800-GAMBLER',
          url: 'tel:**************',
          phone: '1-800-GAMBLER'
        },
        {
          title: 'Self-exclusion options',
          url: '/self-exclusion'
        },
        {
          title: 'Account limits/settings',
          url: '/account/limits'
        }
      ]
    }
  }

  return (
    <footer className={styles.footer}>
      <div className={styles.container}>
        <div className={styles.content}>
          {/* Logo Section */}
          <FooterLogoWidget market={market} data={logoData} />
          
          {/* Navigation Section */}
          <FooterNavigationWidget market={market} data={navigationData} />
          
          {/* Social Media Section */}
          <FooterSocialWidget market={market} data={socialData} />
        </div>
        
        {/* Disclaimer Section - Full Width */}
        <FooterDisclaimerWidget market={market} data={disclaimerData} />
      </div>
    </footer>
  )
}
