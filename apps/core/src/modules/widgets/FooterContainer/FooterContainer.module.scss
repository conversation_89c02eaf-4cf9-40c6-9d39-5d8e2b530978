@use '@theme/functions.scss' as *;

.footer {
  background-color: var(--color-background-secondary, #1a1a1a);
  color: var(--color-text-primary, #ffffff);
  padding: calculate-rem(48px) 0;
  margin-top: auto;
}

.container {
  max-width: calculate-rem(1200px);
  margin: 0 auto;
  padding: 0 calculate-rem(24px);
}

.content {
  display: grid;
  grid-template-columns: 1fr;
  gap: calculate-rem(32px);
  margin-bottom: calculate-rem(32px);

  @media (min-width: 768px) {
    grid-template-columns: 1fr 2fr 1fr;
    grid-template-areas: 
      "logo navigation social";
    align-items: start;
    gap: calculate-rem(32px);
  }

  @media (min-width: 1024px) {
    grid-template-columns: 1fr 3fr 1fr;
    gap: calculate-rem(48px);
  }
}

.error {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: calculate-rem(48px);
  color: var(--color-error, #ef4444);
  font-size: calculate-rem(14px);
  text-align: center;
}

// Responsive adjustments
@media (max-width: 767px) {
  .footer {
    padding: calculate-rem(32px) 0;
  }

  .content {
    gap: calculate-rem(24px);
    margin-bottom: calculate-rem(24px);
  }
}
