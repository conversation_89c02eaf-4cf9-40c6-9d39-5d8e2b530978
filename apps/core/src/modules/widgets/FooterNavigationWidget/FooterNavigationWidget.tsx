'use client'

import type { FC } from 'react'
import React, { useEffect, useState } from 'react'
import type { IDynamicComponentProps } from '@/types/components'
import type { FooterSection, GetFooterConfigLegacyResponse } from '@repo/types/api/s3/footer-config'
import styles from '@modules/widgets/FooterNavigationWidget/FooterNavigationWidget.module.scss'

export const FooterNavigationWidget: FC<IDynamicComponentProps> = ({ market }) => {
  const [navigationSections, setNavigationSections] = useState<FooterSection[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchNavigationConfig = async () => {
      try {
        // For now using mock data, later this will fetch from API
        // In production: const response = await fetch(`/api/s3/footer-config/${market}`)
        const mockNavigationSections: FooterSection[] = [
          {
            title: 'Games',
            links: [
              { title: 'Hold & Win', url: '/games/hold-win' },
              { title: 'Top performing', url: '/games/top-performing' },
              { title: 'All slots', url: '/games/slots' },
              { title: 'Featured', url: '/games/featured' },
              { title: 'New', url: '/games/new' }
            ]
          },
          {
            title: 'Other pages',
            links: [
              { title: 'Bonus buy', url: '/bonus-buy' },
              { title: 'Providers', url: '/providers' },
              { title: 'Blackjack', url: '/games/blackjack' }
            ]
          },
          {
            title: 'More',
            links: [
              { title: 'Promotions', url: '/promotions' },
              { title: 'Coin Store', url: '/coin-store' },
              { title: 'RG Limits', url: '/responsible-gaming' },
              { title: 'Leaderboard', url: '/leaderboard' },
              { title: 'Challenges', url: '/challenges' },
              { title: 'Jackpots', url: '/jackpots' }
            ]
          },
          {
            title: 'Documents',
            links: [
              { title: 'Terms & Conditions', url: '/terms', target: '_blank' },
              { title: 'Privacy Policy', url: '/privacy', target: '_blank' },
              { title: 'AML policy', url: '/aml', target: '_blank' },
              { title: 'Fairness', url: '/fairness', target: '_blank' }
            ]
          }
        ]
        setNavigationSections(mockNavigationSections)
      } catch (error) {
        console.error('Failed to fetch navigation config:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchNavigationConfig()
  }, [market])

  if (loading) {
    return <div className={styles.loading}>Loading navigation...</div>
  }

  if (!navigationSections.length) {
    return null
  }

  return (
    <div className={styles.navigationWidget}>
      {navigationSections.map((section, sectionIndex) => (
        <div key={sectionIndex} className={styles.navigationSection}>
          <h3 className={styles.sectionTitle}>{section.title}</h3>
          <ul className={styles.linkList}>
            {section.links.map((link, linkIndex) => (
              <li key={linkIndex}>
                <a
                  href={link.url}
                  target={link.target || '_self'}
                  rel={link.target === '_blank' ? 'noopener noreferrer' : undefined}
                  className={styles.footerLink}
                >
                  {link.title}
                </a>
              </li>
            ))}
          </ul>
        </div>
      ))}
    </div>
  )
}
