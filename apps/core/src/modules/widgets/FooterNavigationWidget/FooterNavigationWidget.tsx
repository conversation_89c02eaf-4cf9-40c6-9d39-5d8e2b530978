import type { FC } from 'react'
import React from 'react'
import { getFooterConfig } from '@/network/server-utils/s3/getters'
import type { IDynamicComponentProps } from '@/types/components'
import type { FooterSection } from '@repo/types/api/s3/footer-config'
import styles from '@modules/widgets/FooterNavigationWidget/FooterNavigationWidget.module.scss'

export const FooterNavigationWidget: FC<IDynamicComponentProps> = async ({ market }) => {
  let navigationSections: FooterSection[] = []

  try {
    const footerConfig = await getFooterConfig(market)
    const footerItems = footerConfig?.new_footer_items || footerConfig?.footer_items || []
    const navigationSection = footerItems.find(item => item.type === 'navigation_section')
    navigationSections = Array.isArray(navigationSection?.items) ? navigationSection.items as FooterSection[] : []
  } catch (error) {
    console.error('Failed to fetch footer config for navigation:', error)
    return null
  }

  if (!navigationSections || !navigationSections.length) {
    return null
  }

  return (
    <div className={styles.navigationWidget}>
      {navigationSections.map((section, sectionIndex) => (
        <div key={sectionIndex} className={styles.navigationSection}>
          <h3 className={styles.sectionTitle}>{section.title}</h3>
          <ul className={styles.linkList}>
            {section.links.map((link, linkIndex) => (
              <li key={linkIndex}>
                <a
                  href={link.url}
                  target={link.target || '_self'}
                  rel={link.target === '_blank' ? 'noopener noreferrer' : undefined}
                  className={styles.footerLink}
                >
                  {link.title}
                </a>
              </li>
            ))}
          </ul>
        </div>
      ))}
    </div>
  )
}
