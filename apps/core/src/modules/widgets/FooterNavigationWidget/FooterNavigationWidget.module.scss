@use '@theme/functions.scss' as *;

.navigationWidget {
  // Position this widget in the footer layout
  grid-area: navigation;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(calculate-rem(150px), 1fr));
  gap: calculate-rem(32px);
  
  // For mobile, stack vertically
  @media (max-width: 767px) {
    grid-template-columns: 1fr;
    gap: calculate-rem(24px);
    margin-bottom: calculate-rem(32px);
  }
  
  // For tablet, 2 columns
  @media (min-width: 768px) and (max-width: 1023px) {
    grid-template-columns: repeat(2, 1fr);
    gap: calculate-rem(24px);
    margin-bottom: calculate-rem(32px);
  }
  
  // For desktop, 4 columns (or as many as needed)
  @media (min-width: 1024px) {
    grid-template-columns: repeat(4, 1fr);
    gap: calculate-rem(32px);
  }
}

.navigationSection {
  display: flex;
  flex-direction: column;
  gap: calculate-rem(16px);
}

.sectionTitle {
  font-size: calculate-rem(16px);
  font-weight: 600;
  color: var(--color-text-primary, #ffffff);
  margin: 0 0 calculate-rem(16px) 0;
  line-height: 1.3;
}

.linkList {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: calculate-rem(12px);
}

.footerLink {
  color: var(--color-text-secondary, #9ca3af);
  text-decoration: none;
  font-size: calculate-rem(14px);
  line-height: 1.5;
  transition: color 0.2s ease;
  display: inline-block;

  &:hover {
    color: var(--color-text-primary, #ffffff);
  }

  &:focus {
    outline: 2px solid var(--color-accent, #fbbf24);
    outline-offset: 2px;
    border-radius: calculate-rem(2px);
  }
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: calculate-rem(20px);
  color: var(--color-text-secondary, #9ca3af);
  font-size: calculate-rem(14px);
  grid-area: navigation;
}

// Responsive adjustments for mobile
@media (max-width: 767px) {
  .navigationSection {
    text-align: center;
  }
  
  .sectionTitle {
    font-size: calculate-rem(18px);
    margin-bottom: calculate-rem(12px);
  }
  
  .linkList {
    gap: calculate-rem(8px);
  }
}
