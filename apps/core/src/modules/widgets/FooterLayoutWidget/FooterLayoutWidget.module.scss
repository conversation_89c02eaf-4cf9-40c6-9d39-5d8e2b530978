@use '@theme/functions.scss' as *;

.footer {
  background-color: var(--color-background-secondary, #1a1a1a);
  color: var(--color-text-primary, #ffffff);
  padding: calculate-rem(48px) 0;
  margin-top: auto;
}

.container {
  max-width: calculate-rem(1200px);
  margin: 0 auto;
  padding: 0 calculate-rem(24px);
}

.content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: calculate-rem(48px);
  margin-bottom: calculate-rem(32px);

  @media (max-width: 768px) {
    flex-direction: column;
    gap: calculate-rem(32px);
  }
}

.leftSection {
  display: flex;
  flex-direction: column;
  gap: calculate-rem(24px);
  min-width: calculate-rem(200px);
}

// Logo Section
.logoSection {
  display: flex;
  flex-direction: column;
  gap: calculate-rem(16px);
}

.logoContainer {
  display: flex;
  align-items: center;
}

.logoLink {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: inherit;
  
  &:hover {
    opacity: 0.8;
  }
}

.logoText {
  font-size: calculate-rem(24px);
  font-weight: 700;
  letter-spacing: calculate-rem(1px);
  color: var(--color-text-primary, #ffffff);
}

.logoSuperscript {
  font-size: calculate-rem(12px);
  margin-left: calculate-rem(4px);
  color: var(--color-text-secondary, #9ca3af);
}

// Social Media
.socialMedia {
  display: flex;
  gap: calculate-rem(12px);
  flex-wrap: wrap;
}

.socialLink {
  display: flex;
  align-items: center;
  justify-content: center;
  width: calculate-rem(40px);
  height: calculate-rem(40px);
  border-radius: calculate-rem(8px);
  background-color: var(--color-surface-100, #374151);
  color: var(--color-text-primary, #ffffff);
  text-decoration: none;
  transition: all 0.2s ease;

  &:hover {
    background-color: var(--color-surface-200, #4b5563);
    transform: translateY(calculate-rem(-2px));
  }

  &:active {
    transform: translateY(0);
  }
}

// Navigation
.navigationArea {
  display: flex;
  gap: calculate-rem(48px);
  flex: 1;

  @media (max-width: 1024px) {
    gap: calculate-rem(32px);
  }

  @media (max-width: 768px) {
    flex-direction: column;
    gap: calculate-rem(24px);
  }
}

.navigationSection {
  display: flex;
  flex-direction: column;
  gap: calculate-rem(16px);
  min-width: calculate-rem(150px);
}

.sectionTitle {
  font-size: calculate-rem(16px);
  font-weight: 600;
  color: var(--color-text-primary, #ffffff);
  margin: 0;
  margin-bottom: calculate-rem(8px);
}

.linkList {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: calculate-rem(8px);
}

.footerLink {
  font-size: calculate-rem(14px);
  color: var(--color-text-secondary, #9ca3af);
  text-decoration: none;
  line-height: 1.5;
  transition: color 0.2s ease;

  &:hover {
    color: var(--color-text-primary, #ffffff);
  }

  &:focus {
    outline: 2px solid var(--color-primary, #3b82f6);
    outline-offset: 2px;
    border-radius: calculate-rem(4px);
  }
}

// Disclaimer Section
.disclaimerSection {
  border-top: 1px solid var(--color-border, #374151);
  padding-top: calculate-rem(24px);
  display: flex;
  flex-direction: column;
  gap: calculate-rem(16px);
}

.responsibleGambling {
  display: flex;
  flex-direction: column;
  gap: calculate-rem(12px);

  @media (min-width: 768px) {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
}

.rgMessage {
  font-size: calculate-rem(14px);
  font-weight: 600;
  color: var(--color-text-primary, #ffffff);
  margin: 0;
}

.rgLinks {
  display: flex;
  flex-wrap: wrap;
  gap: calculate-rem(16px);

  @media (max-width: 768px) {
    gap: calculate-rem(12px);
  }
}

.rgLink {
  font-size: calculate-rem(12px);
  color: var(--color-text-secondary, #9ca3af);
  text-decoration: underline;
  transition: color 0.2s ease;

  &:hover {
    color: var(--color-text-primary, #ffffff);
  }

  &:focus {
    outline: 2px solid var(--color-primary, #3b82f6);
    outline-offset: 2px;
    border-radius: calculate-rem(4px);
  }
}

.disclaimer {
  display: flex;
  flex-direction: column;
  gap: calculate-rem(12px);
}

.disclaimerText {
  font-size: calculate-rem(12px);
  color: var(--color-text-secondary, #9ca3af);
  line-height: 1.5;
  margin: 0;
}

.companyInfo {
  display: flex;
  flex-direction: column;
  gap: calculate-rem(8px);
}

.companyText {
  font-size: calculate-rem(11px);
  color: var(--color-text-secondary, #9ca3af);
  line-height: 1.4;
  margin: 0;
}

// Responsive Design
@media (max-width: 768px) {
  .footer {
    padding: calculate-rem(32px) 0;
  }

  .container {
    padding: 0 calculate-rem(16px);
  }

  .content {
    margin-bottom: calculate-rem(24px);
  }

  .leftSection {
    align-items: center;
    text-align: center;
  }

  .socialMedia {
    justify-content: center;
  }

  .navigationArea {
    width: 100%;
  }

  .navigationSection {
    text-align: center;
  }

  .responsibleGambling {
    text-align: center;
  }

  .rgLinks {
    justify-content: center;
  }
}
