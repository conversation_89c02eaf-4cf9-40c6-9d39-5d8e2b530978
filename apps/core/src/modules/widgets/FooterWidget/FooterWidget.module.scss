@use '@theme/functions.scss' as *;

.footer {
  background-color: var(--color-background-secondary, #1a1a1a);
  color: var(--color-text-primary, #ffffff);
  padding: calculate-rem(48px) 0;
  margin-top: auto;
}

.container {
  max-width: calculate-rem(1200px);
  margin: 0 auto;
  padding: 0 calculate-rem(24px);
}

.content {
  display: grid;
  grid-template-columns: 1fr;
  gap: calculate-rem(32px);
  margin-bottom: calculate-rem(32px);

  @media (min-width: 768px) {
    grid-template-columns: 1fr 2fr;
    grid-template-areas: 
      "logo navigation";
    align-items: start;
    gap: calculate-rem(32px);
  }

  @media (min-width: 1024px) {
    grid-template-columns: 1fr 3fr;
    gap: calculate-rem(48px);
  }
}

// Logo Section (Logo + Social Icons)
.logoSection {
  grid-area: logo;
  display: flex;
  flex-direction: column;
  gap: calculate-rem(24px);

  @media (max-width: 767px) {
    align-items: center;
    text-align: center;
  }

  @media (min-width: 768px) {
    align-items: flex-start;
  }
}

.logoContainer {
  display: flex;
  align-items: center;
}

.logoLink {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: inherit;
  font-weight: 700;
  font-size: calculate-rem(24px);
  line-height: 1.2;

  &:hover {
    opacity: 0.8;
  }
}

.logoText {
  color: var(--color-text-primary, #ffffff);
}

.logoSuperscript {
  font-size: calculate-rem(12px);
  margin-left: calculate-rem(4px);
  color: var(--color-text-secondary, #9ca3af);
}

// Social Media
.socialMedia {
  display: flex;
  gap: calculate-rem(16px);
  flex-wrap: wrap;
  align-items: center;
}

.socialLink {
  display: flex;
  align-items: center;
  justify-content: center;
  width: calculate-rem(40px);
  height: calculate-rem(40px);
  border-radius: calculate-rem(8px);
  background-color: var(--color-surface-100, #374151);
  color: var(--color-text-primary, #ffffff);
  text-decoration: none;
  transition: all 0.2s ease;

  &:hover {
    background-color: var(--color-primary, #3b82f6);
    transform: translateY(-2px);
  }
}

// Navigation Area
.navigationArea {
  grid-area: navigation;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(calculate-rem(150px), 1fr));
  gap: calculate-rem(32px);
  
  @media (max-width: 767px) {
    grid-template-columns: 1fr;
    gap: calculate-rem(24px);
  }
  
  @media (min-width: 768px) and (max-width: 1023px) {
    grid-template-columns: repeat(2, 1fr);
    gap: calculate-rem(24px);
  }
  
  @media (min-width: 1024px) {
    grid-template-columns: repeat(4, 1fr);
    gap: calculate-rem(32px);
  }
}

.navigationSection {
  display: flex;
  flex-direction: column;
  gap: calculate-rem(16px);
}

.sectionTitle {
  font-size: calculate-rem(16px);
  font-weight: 600;
  color: var(--color-text-primary, #ffffff);
  margin: 0;
  line-height: 1.4;
}

.linkList {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: calculate-rem(12px);
}

.footerLink {
  color: var(--color-text-secondary, #9ca3af);
  text-decoration: none;
  font-size: calculate-rem(14px);
  line-height: 1.4;
  transition: color 0.2s ease;

  &:hover {
    color: var(--color-text-primary, #ffffff);
  }
}

// Disclaimer Section
.disclaimerSection {
  border-top: 1px solid var(--color-border, #374151);
  padding-top: calculate-rem(32px);
  display: flex;
  flex-direction: column;
  gap: calculate-rem(24px);
}

.responsibleGambling {
  display: flex;
  flex-direction: column;
  gap: calculate-rem(16px);
  
  @media (min-width: 768px) {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
}

.rgMessage {
  font-size: calculate-rem(14px);
  font-weight: 600;
  color: var(--color-text-primary, #ffffff);
  margin: 0;
}

.rgLinks {
  display: flex;
  flex-wrap: wrap;
  gap: calculate-rem(16px);
  
  @media (max-width: 767px) {
    justify-content: center;
  }
}

.rgLink {
  color: var(--color-text-secondary, #9ca3af);
  text-decoration: none;
  font-size: calculate-rem(12px);
  padding: calculate-rem(4px) calculate-rem(8px);
  border-radius: calculate-rem(4px);
  border: 1px solid var(--color-border, #374151);
  transition: all 0.2s ease;

  &:hover {
    color: var(--color-text-primary, #ffffff);
    border-color: var(--color-primary, #3b82f6);
  }
}

.disclaimer {
  text-align: center;
}

.disclaimerText {
  font-size: calculate-rem(12px);
  color: var(--color-text-secondary, #9ca3af);
  line-height: 1.5;
  margin: 0 0 calculate-rem(16px) 0;
}

.companyInfo {
  display: flex;
  flex-direction: column;
  gap: calculate-rem(8px);
  margin-top: calculate-rem(16px);
}

.companyText {
  font-size: calculate-rem(11px);
  color: var(--color-text-secondary, #9ca3af);
  line-height: 1.4;
  margin: 0;
}

.ageDisclaimer {
  font-size: calculate-rem(11px);
  color: var(--color-text-secondary, #9ca3af);
  font-weight: 600;
  margin: 0;
}

// Responsive adjustments
@media (max-width: 767px) {
  .footer {
    padding: calculate-rem(32px) 0;
  }

  .content {
    gap: calculate-rem(24px);
    margin-bottom: calculate-rem(24px);
  }

  .navigationSection {
    text-align: center;
  }
}
