@use '@theme/functions.scss' as *;

.footer {
  background-color: var(--color-background-secondary, #1a1a1a);
  color: var(--color-text-primary, #ffffff);
  padding: calculate-rem(48px) 0;
  margin-top: auto;
}

.container {
  max-width: calculate-rem(1200px);
  margin: 0 auto;
  padding: 0 calculate-rem(24px);
}

.content {
  display: grid;
  grid-template-columns: 1fr;
  gap: calculate-rem(32px);

  @media (min-width: 768px) {
    grid-template-columns: 1fr 1fr;
    gap: calculate-rem(32px);
  }

  @media (min-width: 1024px) {
    grid-template-columns: 1fr repeat(4, 1fr);
    gap: calculate-rem(32px);
  }
}

.logoSection {
  display: flex;
  flex-direction: column;
  gap: calculate-rem(24px);

  @media (min-width: 1024px) {
    grid-column: 1;
  }
}

.logoContainer {
  margin-bottom: calculate-rem(24px);
}

.logoLink {
  text-decoration: none;
  color: inherit;
  display: inline-block;
  transition: opacity 0.2s ease;

  &:hover {
    opacity: 0.8;
  }

  &:focus {
    outline: 2px solid var(--color-accent, #fbbf24);
    outline-offset: 2px;
    border-radius: calculate-rem(4px);
  }
}

.logoText {
  font-size: calculate-rem(24px);
  font-weight: 700;
  color: var(--color-accent, #fbbf24);
  letter-spacing: 0.05em;
}

.logoSuperscript {
  font-size: calculate-rem(12px);
  color: var(--color-text-secondary, #9ca3af);
  margin-left: calculate-rem(4px);
  vertical-align: super;
}

.socialMedia {
  display: flex;
  gap: calculate-rem(16px);
  flex-wrap: wrap;
}

.socialLink {
  color: var(--color-text-secondary, #9ca3af);
  transition: color 0.2s ease, transform 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: calculate-rem(8px);
  border-radius: calculate-rem(4px);

  &:hover {
    color: var(--color-text-primary, #ffffff);
    transform: translateY(-2px);
  }

  &:focus {
    outline: 2px solid var(--color-accent, #fbbf24);
    outline-offset: 2px;
  }
}

.navigationSection {
  display: flex;
  flex-direction: column;
  gap: calculate-rem(16px);
}

.sectionTitle {
  font-size: calculate-rem(16px);
  font-weight: 600;
  color: var(--color-text-primary, #ffffff);
  margin: 0 0 calculate-rem(16px) 0;
}

.linkList {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: calculate-rem(12px);
}

.footerLink {
  color: var(--color-text-secondary, #9ca3af);
  text-decoration: none;
  font-size: calculate-rem(14px);
  line-height: 1.5;
  transition: color 0.2s ease;
  display: inline-block;

  &:hover {
    color: var(--color-text-primary, #ffffff);
  }

  &:focus {
    outline: 2px solid var(--color-accent, #fbbf24);
    outline-offset: 2px;
    border-radius: calculate-rem(2px);
  }
}

.disclaimer {
  margin-top: calculate-rem(48px);
  padding-top: calculate-rem(32px);
  border-top: 1px solid var(--color-border, #374151);
}

.disclaimerText {
  font-size: calculate-rem(12px);
  line-height: 1.6;
  color: var(--color-text-tertiary, #6b7280);
  margin: 0;
  max-width: none;

  br {
    display: block;
    margin: calculate-rem(8px) 0;
    content: "";
  }
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: calculate-rem(48px);
  color: var(--color-text-secondary, #9ca3af);
  font-size: calculate-rem(14px);
}

.error {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: calculate-rem(48px);
  color: var(--color-error, #ef4444);
  font-size: calculate-rem(14px);
}

// Responsive adjustments
@media (max-width: 767px) {
  .footer {
    padding: calculate-rem(32px) 0;
  }

  .content {
    gap: calculate-rem(24px);
  }

  .logoSection {
    text-align: center;
  }

  .socialMedia {
    justify-content: center;
  }

  .navigationSection {
    text-align: center;
  }

  .disclaimer {
    margin-top: calculate-rem(32px);
    padding-top: calculate-rem(24px);
  }
}
