import type { FC } from 'react'
import React from 'react'
import { Facebook, Twitter, Twitch, MessageCircle, Send, Instagram, Youtube, Video } from 'lucide-react'
import { getFooterConfig } from '@/network/server-utils/s3/getters'
import type { IDynamicComponentProps } from '@/types/components'
import type { FooterSection, FooterSocialMedia } from '@repo/types/api/s3/footer-config'
import styles from '@modules/widgets/FooterWidget/FooterWidget.module.scss'

const getSocialIcon = (iconName: string) => {
  const iconMap = {
    facebook: Facebook,
    twitter: Twitter,
    twitch: Twitch,
    'message-circle': MessageCircle,
    send: Send,
    instagram: Instagram,
    youtube: Youtube,
    video: Video, // For TikTok
  }
  return iconMap[iconName as keyof typeof iconMap] || MessageCircle
}

export const FooterWidget: FC<IDynamicComponentProps> = async ({ market }) => {
  let footerConfig = null

  try {
    footerConfig = await getFooterConfig(market)
  } catch (error) {
    console.error('Failed to fetch footer config:', error)
    return null
  }

  if (!footerConfig?.footer_items) {
    return null
  }

  // Extract sections using proper types
  const socialMediaSection = footerConfig.footer_items.find(item => item.type === 'social_media')
  const navigationSection = footerConfig.footer_items.find(item => item.type === 'navigation_section')
  const disclaimerSection = footerConfig.footer_items.find(item => item.type === 'disclaimer')

  const socialMediaItems = Array.isArray(socialMediaSection?.items) ? socialMediaSection.items as FooterSocialMedia[] : []
  const navigationSections = Array.isArray(navigationSection?.items) ? navigationSection.items as FooterSection[] : []
  const disclaimerText = typeof disclaimerSection?.items === 'string' ? disclaimerSection.items : ''

  return (
    <footer className={styles.footer}>
      <div className={styles.container}>
        <div className={styles.content}>
          {/* Logo Section */}
          <div className={styles.logoSection}>
            <div className={styles.logoContainer}>
              <a href="/" className={styles.logoLink} aria-label="Lucky One Casino - Return to Homepage">
                <span className={styles.logoText}>LUCKY ONE</span>
                <sup className={styles.logoSuperscript}>US</sup>
              </a>
            </div>

            {/* Social Media Icons */}
            {socialMediaItems.length > 0 && (
              <div className={styles.socialMedia}>
                {socialMediaItems.map((social, index) => {
                  const IconComponent = getSocialIcon(social.icon)
                  return (
                    <a
                      key={index}
                      href={social.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={styles.socialLink}
                      aria-label={`Visit our ${social.platform} page`}
                    >
                      <IconComponent size={20} />
                    </a>
                  )
                })}
              </div>
            )}
          </div>

          {/* Navigation Sections */}
          <div className={styles.navigationArea}>
            {navigationSections.map((section, sectionIndex) => (
              <div key={sectionIndex} className={styles.navigationSection}>
                <h3 className={styles.sectionTitle}>{section.title}</h3>
                <ul className={styles.linkList}>
                  {section.links.map((link, linkIndex) => (
                    <li key={linkIndex}>
                      <a
                        href={link.url}
                        target={link.target || '_self'}
                        rel={link.target === '_blank' ? 'noopener noreferrer' : undefined}
                        className={styles.footerLink}
                      >
                        {link.title}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>

        {/* Disclaimer Section */}
        <div className={styles.disclaimerSection}>
          {/* Responsible Gambling */}
          <div className={styles.responsibleGambling}>
            <p className={styles.rgMessage}>Play Responsibly</p>
            <div className={styles.rgLinks}>
              <a
                href="https://www.ncpgambling.org/"
                target="_blank"
                rel="noopener noreferrer"
                className={styles.rgLink}
              >
                National Council on Problem Gambling
              </a>
              <a
                href="tel:**************"
                className={styles.rgLink}
              >
                1-800-GAMBLER
              </a>
              <a
                href="/self-exclusion"
                className={styles.rgLink}
              >
                Self-exclusion options
              </a>
              <a
                href="/account/limits"
                className={styles.rgLink}
              >
                Account limits/settings
              </a>
            </div>
          </div>

          {/* Main Disclaimer */}
          {disclaimerText && (
            <div className={styles.disclaimer}>
              <p className={styles.disclaimerText}>
                {disclaimerText}
              </p>
            </div>
          )}
        </div>
      </div>
    </footer>
  )
}
