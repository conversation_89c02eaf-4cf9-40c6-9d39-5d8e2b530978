'use client'

import type { FC } from 'react'
import React, { useEffect, useState } from 'react'
import { Facebook, Twitter, Twitch, MessageCircle, Send, Instagram, Youtube } from 'lucide-react'
import type { IDynamicComponentProps } from '@/types/components'
import type { FooterSection, FooterSocialMedia, GetFooterConfigLegacyResponse } from '@repo/types/api/s3/footer-config'
import styles from '@modules/widgets/FooterWidget/FooterWidget.module.scss'

const getSocialIcon = (iconName: string) => {
  const iconMap = {
    facebook: Facebook,
    twitter: Twitter,
    twitch: Twitch,
    'message-circle': MessageCircle,
    send: Send,
    instagram: Instagram,
    youtube: Youtube,
  }
  return iconMap[iconName as keyof typeof iconMap] || MessageCircle
}

export const FooterWidget: FC<IDynamicComponentProps> = ({ market }) => {
  const [footerConfig, setFooterConfig] = useState<GetFooterConfigLegacyResponse | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchFooterConfig = async () => {
      try {
        // For now, we'll use a mock configuration
        // In a real implementation, this would fetch from the API
        const mockConfig: GetFooterConfigLegacyResponse = {
          footer_items: [],
          new_footer_items: [
            {
              type: 'navigation_section',
              items: [
                {
                  title: 'Games',
                  links: [
                    { title: 'Hold & Win', url: '/games/hold-win' },
                    { title: 'Top performing', url: '/games/top-performing' },
                    { title: 'All slots', url: '/games/slots' },
                    { title: 'Featured', url: '/games/featured' },
                    { title: 'New', url: '/games/new' }
                  ]
                },
                {
                  title: 'Other pages',
                  links: [
                    { title: 'Bonus buy', url: '/bonus-buy' },
                    { title: 'Providers', url: '/providers' },
                    { title: 'Blackjack', url: '/games/blackjack' }
                  ]
                },
                {
                  title: 'Documents',
                  links: [
                    { title: 'Terms & Conditions', url: '/terms', target: '_blank' },
                    { title: 'Privacy Policy', url: '/privacy', target: '_blank' },
                    { title: 'AML policy', url: '/aml', target: '_blank' },
                    { title: 'Fairness', url: '/fairness', target: '_blank' }
                  ]
                }
              ]
            },
            {
              type: 'social_media',
              items: [
                { platform: 'Facebook', url: 'https://facebook.com/luckyone', icon: 'facebook' },
                { platform: 'Twitter', url: 'https://twitter.com/luckyone', icon: 'twitter' },
                { platform: 'Twitch', url: 'https://twitch.tv/luckyone', icon: 'twitch' },
                { platform: 'Discord', url: 'https://discord.gg/luckyone', icon: 'message-circle' },
                { platform: 'Telegram', url: 'https://t.me/luckyone', icon: 'send' }
              ]
            },
            {
              type: 'disclaimer',
              items: 'You understand that you are providing information to Dream Labs Entertainment LLC. The information you provide will only be used to administer this promotion. NO PURCHASE NECESSARY to enter Sweepstakes. SWEEPSTAKES ARE VOID WHERE PROHIBITED BY LAW. For detailed rules, see Sweeps Rules'
            }
          ]
        }
        setFooterConfig(mockConfig)
      } catch (error) {
        console.error('Failed to fetch footer config:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchFooterConfig()
  }, [market])

  if (loading) {
    return <div className={styles.loading}>Loading footer...</div>
  }

  if (!footerConfig) {
    return <div className={styles.error}>Failed to load footer configuration</div>
  }

  // Use new_footer_items if available, fallback to footer_items
  const footerItems = footerConfig.new_footer_items || footerConfig.footer_items || []

  const navigationSection = footerItems.find(item => item.type === 'navigation_section')
  const socialMediaSection = footerItems.find(item => item.type === 'social_media')
  const disclaimerSection = footerItems.find(item => item.type === 'disclaimer')

  const navigationSections = Array.isArray(navigationSection?.items) ? navigationSection.items as FooterSection[] : []
  const socialMediaItems = Array.isArray(socialMediaSection?.items) ? socialMediaSection.items as FooterSocialMedia[] : []
  const disclaimerText = typeof disclaimerSection?.items === 'string' ? disclaimerSection.items : ''

  return (
    <footer className={styles.footer}>
      <div className={styles.container}>
        <div className={styles.content}>
          {/* Logo Section */}
          <div className={styles.logoSection}>
            <div className={styles.logoContainer}>
              <a href="/" className={styles.logoLink} aria-label="Go to homepage">
                <span className={styles.logoText}>LUCKY ONE</span>
                <sup className={styles.logoSuperscript}>US</sup>
              </a>
            </div>

            {/* Social Media Icons */}
            {socialMediaItems.length > 0 && (
              <div className={styles.socialMedia}>
                {socialMediaItems.map((social, index) => {
                  const IconComponent = getSocialIcon(social.icon)
                  return (
                    <a
                      key={index}
                      href={social.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={styles.socialLink}
                      aria-label={`Visit our ${social.platform} page`}
                    >
                      <IconComponent size={20} />
                    </a>
                  )
                })}
              </div>
            )}
          </div>

          {/* Navigation Sections */}
          {navigationSections.map((section, sectionIndex) => (
            <div key={sectionIndex} className={styles.navigationSection}>
              <h3 className={styles.sectionTitle}>{section.title}</h3>
              <ul className={styles.linkList}>
                {section.links.map((link, linkIndex) => (
                  <li key={linkIndex}>
                    <a
                      href={link.url}
                      target={link.target || '_self'}
                      rel={link.target === '_blank' ? 'noopener noreferrer' : undefined}
                      className={styles.footerLink}
                    >
                      {link.title}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Disclaimer */}
        {disclaimerText && (
          <div className={styles.disclaimer}>
            <p 
              className={styles.disclaimerText}
              dangerouslySetInnerHTML={{ __html: disclaimerText }}
            />
          </div>
        )}
      </div>
    </footer>
  )
}
