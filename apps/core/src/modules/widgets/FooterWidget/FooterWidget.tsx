import type { FC } from 'react'
import React from 'react'
import { Facebook, Twitter, Twitch, MessageCircle, Send, Instagram, Youtube } from 'lucide-react'
import { getFooterConfig } from '@/network/server-utils/s3/getters'
import type { IDynamicComponentProps } from '@/types/components'
import type { FooterSection, FooterSocialMedia } from '@repo/types/api/s3/footer-config'
import styles from '@modules/widgets/FooterWidget/FooterWidget.module.scss'

const getSocialIcon = (iconName: string) => {
  const iconMap = {
    facebook: Facebook,
    twitter: Twitter,
    twitch: Twitch,
    'message-circle': MessageCircle,
    send: Send,
    instagram: Instagram,
    youtube: Youtube,
  }
  return iconMap[iconName as keyof typeof iconMap] || MessageCircle
}

export const FooterWidget: FC<IDynamicComponentProps> = async ({ market }) => {
  let footerData = null

  try {
    const footerConfig = await getFooterConfig(market)
    const footerItems = footerConfig?.new_footer_items || footerConfig?.footer_items || []
    
    const navigationSection = footerItems.find(item => item.type === 'navigation_section')
    const socialMediaSection = footerItems.find(item => item.type === 'social_media')
    const disclaimerSection = footerItems.find(item => item.type === 'disclaimer')

    footerData = {
      logo: {
        name: 'LUCKY ONE',
        superscript: 'US',
        href: '/'
      },
      navigationSections: Array.isArray(navigationSection?.items) ? navigationSection.items as FooterSection[] : [],
      socialMediaItems: Array.isArray(socialMediaSection?.items) ? socialMediaSection.items as FooterSocialMedia[] : [],
      disclaimer: {
        text: typeof disclaimerSection?.items === 'string' ? disclaimerSection.items : '',
        responsibleGambling: {
          message: 'Play Responsibly',
          links: [
            { title: 'National Council on Problem Gambling', url: 'https://www.ncpgambling.org/' },
            { title: '1-800-GAMBLER', url: 'tel:**************', phone: '1-800-GAMBLER' },
            { title: 'Self-exclusion options', url: '/self-exclusion' },
            { title: 'Account limits/settings', url: '/account/limits' }
          ]
        }
      }
    }
  } catch (error) {
    console.error('Failed to fetch footer config:', error)
    return null
  }

  if (!footerData) {
    return null
  }

  return (
    <footer className={styles.footer}>
      <div className={styles.container}>
        <div className={styles.content}>
          {/* Logo Section */}
          <div className={styles.logoSection}>
            <div className={styles.logoContainer}>
              <a href={footerData.logo.href} className={styles.logoLink} aria-label="Go to homepage">
                <span className={styles.logoText}>{footerData.logo.name}</span>
                {footerData.logo.superscript && (
                  <sup className={styles.logoSuperscript}>{footerData.logo.superscript}</sup>
                )}
              </a>
            </div>

            {/* Social Media Icons */}
            {footerData.socialMediaItems.length > 0 && (
              <div className={styles.socialMedia}>
                {footerData.socialMediaItems.map((social, index) => {
                  const IconComponent = getSocialIcon(social.icon)
                  return (
                    <a
                      key={index}
                      href={social.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={styles.socialLink}
                      aria-label={`Visit our ${social.platform} page`}
                    >
                      <IconComponent size={20} />
                    </a>
                  )
                })}
              </div>
            )}
          </div>

          {/* Navigation Sections */}
          <div className={styles.navigationArea}>
            {footerData.navigationSections.map((section, sectionIndex) => (
              <div key={sectionIndex} className={styles.navigationSection}>
                <h3 className={styles.sectionTitle}>{section.title}</h3>
                <ul className={styles.linkList}>
                  {section.links.map((link, linkIndex) => (
                    <li key={linkIndex}>
                      <a
                        href={link.url}
                        target={link.target || '_self'}
                        rel={link.target === '_blank' ? 'noopener noreferrer' : undefined}
                        className={styles.footerLink}
                      >
                        {link.title}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>

        {/* Disclaimer Section */}
        <div className={styles.disclaimerSection}>
          {/* Responsible Gambling */}
          {footerData.disclaimer.responsibleGambling && (
            <div className={styles.responsibleGambling}>
              <p className={styles.rgMessage}>{footerData.disclaimer.responsibleGambling.message}</p>
              <div className={styles.rgLinks}>
                {footerData.disclaimer.responsibleGambling.links.map((link, index) => (
                  <a
                    key={index}
                    href={link.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={styles.rgLink}
                  >
                    {link.phone || link.title}
                  </a>
                ))}
              </div>
            </div>
          )}
          
          {/* Main Disclaimer */}
          {footerData.disclaimer.text && (
            <div className={styles.disclaimer}>
              <p 
                className={styles.disclaimerText}
                dangerouslySetInnerHTML={{ __html: footerData.disclaimer.text }}
              />
            </div>
          )}
        </div>
      </div>
    </footer>
  )
}
