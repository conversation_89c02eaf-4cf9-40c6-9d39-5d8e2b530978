import type { FC } from 'react'
import React from 'react'
import { Facebook, Twitter, Twitch, MessageCircle, Send, Instagram, Youtube, Video } from 'lucide-react'
import { getFooterConfig } from '@/network/server-utils/s3/getters'
import type { IDynamicComponentProps } from '@/types/components'
import styles from '@modules/widgets/FooterWidget/FooterWidget.module.scss'

const getSocialIcon = (iconName: string) => {
  const iconMap = {
    facebook: Facebook,
    twitter: Twitter,
    twitch: Twitch,
    'message-circle': MessageCircle,
    send: Send,
    instagram: Instagram,
    youtube: Youtube,
    video: Video, // For TikTok
  }
  return iconMap[iconName as keyof typeof iconMap] || MessageCircle
}

export const FooterWidget: FC<IDynamicComponentProps> = async ({ market }) => {
  let footerConfig = null

  try {
    footerConfig = await getFooterConfig(market)
  } catch (error) {
    console.error('Failed to fetch footer config:', error)
    return null
  }

  if (!footerConfig?.sections) {
    return null
  }

  // Filter enabled sections and group by position
  const enabledSections = footerConfig.sections.filter((section: any) => section.enabled)
  const leftSections = enabledSections.filter((section: any) => section.position === 'left').sort((a: any, b: any) => a.order - b.order)
  const centerSections = enabledSections.filter((section: any) => section.position === 'center').sort((a: any, b: any) => a.order - b.order)
  const bottomSections = enabledSections.filter((section: any) => section.position === 'bottom').sort((a: any, b: any) => a.order - b.order)

  // Extract specific section data
  const brandSection = leftSections.find((section: any) => section.type === 'brand-logo')
  const socialSection = leftSections.find((section: any) => section.type === 'social-media')
  const navigationSection = centerSections.find((section: any) => section.type === 'navigation-links')
  const responsibleGamblingSection = bottomSections.find((section: any) => section.type === 'responsible-gambling')
  const disclaimerSection = bottomSections.find((section: any) => section.type === 'legal-disclaimer')

  return (
    <footer className={styles.footer}>
      <div className={styles.container}>
        <div className={styles.content}>
          {/* Left Section - Logo and Social */}
          <div className={styles.logoSection}>
            {/* Brand Logo */}
            {brandSection && (
              <div className={styles.logoContainer}>
                <a
                  href={brandSection.config.logo.href}
                  className={styles.logoLink}
                  aria-label={brandSection.config.logo.alt}
                >
                  <span className={styles.logoText}>{brandSection.config.logo.text}</span>
                  {brandSection.config.logo.superscript && (
                    <sup className={styles.logoSuperscript}>{brandSection.config.logo.superscript}</sup>
                  )}
                </a>
              </div>
            )}

            {/* Social Media Icons */}
            {socialSection && (
              <div className={styles.socialMedia}>
                {socialSection.config.platforms
                  .filter((platform: any) => platform.enabled)
                  .map((social: any, index: number) => {
                    const IconComponent = getSocialIcon(social.icon)
                    return (
                      <a
                        key={index}
                        href={social.url}
                        target={social.target}
                        rel="noopener noreferrer"
                        className={styles.socialLink}
                        aria-label={`Visit our ${social.platform} page`}
                      >
                        <IconComponent size={20} />
                      </a>
                    )
                  })}
              </div>
            )}
          </div>

          {/* Navigation Area */}
          {navigationSection && (
            <div className={styles.navigationArea}>
              {navigationSection.config.columns
                .filter((column: any) => column.enabled)
                .map((column: any, columnIndex: number) => (
                  <div key={columnIndex} className={styles.navigationSection}>
                    <h3 className={styles.sectionTitle}>{column.title}</h3>
                    <ul className={styles.linkList}>
                      {column.links
                        .filter((link: any) => link.enabled)
                        .map((link: any, linkIndex: number) => (
                          <li key={linkIndex}>
                            <a
                              href={link.url}
                              target={link.target}
                              rel={link.target === '_blank' ? 'noopener noreferrer' : undefined}
                              className={styles.footerLink}
                            >
                              {link.title}
                            </a>
                          </li>
                        ))}
                    </ul>
                  </div>
                ))}
            </div>
          )}
        </div>

        {/* Bottom Section - Responsible Gambling and Legal */}
        <div className={styles.disclaimerSection}>
          {/* Responsible Gambling */}
          {responsibleGamblingSection && (
            <div className={styles.responsibleGambling}>
              <p className={styles.rgMessage}>{responsibleGamblingSection.config.message}</p>
              <div className={styles.rgLinks}>
                {responsibleGamblingSection.config.links
                  .filter((link: any) => link.enabled)
                  .map((link: any, index: number) => (
                    <a
                      key={index}
                      href={link.url}
                      target={link.target}
                      rel={link.target === '_blank' ? 'noopener noreferrer' : undefined}
                      className={styles.rgLink}
                    >
                      {link.phone || link.title}
                    </a>
                  ))}
              </div>
            </div>
          )}

          {/* Legal Disclaimer */}
          {disclaimerSection && (
            <div className={styles.disclaimer}>
              <p
                className={styles.disclaimerText}
                dangerouslySetInnerHTML={{ __html: disclaimerSection.config.text }}
              />
              {disclaimerSection.config.showCompanyInfo && (
                <div className={styles.companyInfo}>
                  <p className={styles.companyText}>
                    {disclaimerSection.config.companyInfo.copyright}
                  </p>
                  <p className={styles.companyText}>
                    {disclaimerSection.config.companyInfo.operatedBy} - {disclaimerSection.config.companyInfo.address}
                  </p>
                  {disclaimerSection.config.showAgeDisclaimer && (
                    <p className={styles.ageDisclaimer}>
                      {disclaimerSection.config.companyInfo.ageDisclaimer}
                    </p>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </footer>
  )
}
