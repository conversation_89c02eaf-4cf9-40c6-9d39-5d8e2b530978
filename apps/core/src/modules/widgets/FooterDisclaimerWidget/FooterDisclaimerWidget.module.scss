@use '@theme/functions.scss' as *;

.disclaimerWidget {
  // Position this widget in the footer layout - spans full width at bottom
  grid-area: disclaimer;
  width: 100%;
  margin-top: calculate-rem(32px);
  padding-top: calculate-rem(24px);
  border-top: 1px solid var(--color-border, #374151);
}

.responsibleGambling {
  margin-bottom: calculate-rem(24px);
  padding-bottom: calculate-rem(16px);
  border-bottom: 1px solid var(--color-border-light, #4b5563);
}

.rgMessage {
  font-size: calculate-rem(14px);
  font-weight: 600;
  color: var(--color-text-primary, #ffffff);
  margin: 0 0 calculate-rem(12px) 0;
  text-align: center;
  
  @media (min-width: 768px) {
    text-align: left;
  }
}

.rgLinks {
  display: flex;
  flex-wrap: wrap;
  gap: calculate-rem(16px);
  justify-content: center;
  
  @media (min-width: 768px) {
    justify-content: flex-start;
  }
}

.rgLink {
  color: var(--color-accent, #fbbf24);
  text-decoration: none;
  font-size: calculate-rem(13px);
  font-weight: 500;
  transition: color 0.2s ease;
  padding: calculate-rem(4px) calculate-rem(8px);
  border-radius: calculate-rem(4px);
  border: 1px solid var(--color-accent, #fbbf24);
  
  &:hover {
    background-color: var(--color-accent, #fbbf24);
    color: var(--color-background, #000000);
  }

  &:focus {
    outline: 2px solid var(--color-accent, #fbbf24);
    outline-offset: 2px;
  }
}

.disclaimer {
  margin-top: calculate-rem(16px);
}

.disclaimerText {
  font-size: calculate-rem(12px);
  line-height: 1.6;
  color: var(--color-text-tertiary, #6b7280);
  margin: 0;
  text-align: center;
  
  @media (min-width: 768px) {
    text-align: left;
  }

  br {
    display: block;
    margin: calculate-rem(8px) 0;
    content: "";
  }
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: calculate-rem(20px);
  color: var(--color-text-secondary, #9ca3af);
  font-size: calculate-rem(14px);
  grid-area: disclaimer;
}

// Responsive adjustments
@media (max-width: 767px) {
  .disclaimerWidget {
    margin-top: calculate-rem(24px);
    padding-top: calculate-rem(20px);
  }
  
  .responsibleGambling {
    margin-bottom: calculate-rem(20px);
    padding-bottom: calculate-rem(12px);
  }
  
  .rgLinks {
    gap: calculate-rem(12px);
  }
  
  .rgLink {
    font-size: calculate-rem(12px);
    padding: calculate-rem(6px) calculate-rem(12px);
  }
}
