import type { FC } from 'react'
import React from 'react'
import type { IDynamicComponentProps } from '@/types/components'
import styles from '@modules/widgets/FooterDisclaimerWidget/FooterDisclaimerWidget.module.scss'

interface DisclaimerConfig {
  text: string
  responsibleGambling?: {
    message: string
    links: Array<{
      title: string
      url: string
      phone?: string
    }>
  }
}

export const FooterDisclaimerWidget: FC<IDynamicComponentProps> = ({ data }) => {
  const disclaimerConfig = data as DisclaimerConfig

  if (!disclaimerConfig) {
    return null
  }

  return (
    <div className={styles.disclaimerWidget}>
      {/* Responsible Gambling Section */}
      {disclaimerConfig.responsibleGambling && (
        <div className={styles.responsibleGambling}>
          <p className={styles.rgMessage}>{disclaimerConfig.responsibleGambling.message}</p>
          <div className={styles.rgLinks}>
            {disclaimerConfig.responsibleGambling.links.map((link, index) => (
              <a
                key={index}
                href={link.url}
                target="_blank"
                rel="noopener noreferrer"
                className={styles.rgLink}
              >
                {link.phone || link.title}
              </a>
            ))}
          </div>
        </div>
      )}
      
      {/* Main Disclaimer Text */}
      <div className={styles.disclaimer}>
        <p 
          className={styles.disclaimerText}
          dangerouslySetInnerHTML={{ __html: disclaimerConfig.text }}
        />
      </div>
    </div>
  )
}
