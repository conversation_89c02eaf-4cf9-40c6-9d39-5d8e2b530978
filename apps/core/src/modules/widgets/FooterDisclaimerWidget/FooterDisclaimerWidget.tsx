import type { FC } from 'react'
import React from 'react'
import { getFooterConfig } from '@/network/server-utils/s3/getters'
import type { IDynamicComponentProps } from '@/types/components'
import styles from '@modules/widgets/FooterDisclaimerWidget/FooterDisclaimerWidget.module.scss'

interface DisclaimerConfig {
  text: string
  responsibleGambling?: {
    message: string
    links: Array<{
      title: string
      url: string
      phone?: string
    }>
  }
}

export const FooterDisclaimerWidget: FC<IDynamicComponentProps> = async ({ market }) => {
  let disclaimerConfig: DisclaimerConfig | null = null

  try {
    const footerConfig = await getFooterConfig(market)
    const footerItems = footerConfig?.new_footer_items || footerConfig?.footer_items || []
    const disclaimerSection = footerItems.find(item => item.type === 'disclaimer')

    disclaimerConfig = {
      text: typeof disclaimerSection?.items === 'string' ? disclaimerSection.items : '',
      responsibleGambling: {
        message: 'Play Responsibly',
        links: [
          {
            title: 'National Council on Problem Gambling',
            url: 'https://www.ncpgambling.org/'
          },
          {
            title: '1-800-GAMBLER',
            url: 'tel:**************',
            phone: '1-800-GAMBLER'
          },
          {
            title: 'Self-exclusion options',
            url: '/self-exclusion'
          },
          {
            title: 'Account limits/settings',
            url: '/account/limits'
          }
        ]
      }
    }
  } catch (error) {
    console.error('Failed to fetch footer config for disclaimer:', error)
    return null
  }

  if (!disclaimerConfig) {
    return null
  }

  return (
    <div className={styles.disclaimerWidget}>
      {/* Responsible Gambling Section */}
      {disclaimerConfig.responsibleGambling && (
        <div className={styles.responsibleGambling}>
          <p className={styles.rgMessage}>{disclaimerConfig.responsibleGambling.message}</p>
          <div className={styles.rgLinks}>
            {disclaimerConfig.responsibleGambling.links.map((link, index) => (
              <a
                key={index}
                href={link.url}
                target="_blank"
                rel="noopener noreferrer"
                className={styles.rgLink}
              >
                {link.phone || link.title}
              </a>
            ))}
          </div>
        </div>
      )}
      
      {/* Main Disclaimer Text */}
      <div className={styles.disclaimer}>
        <p 
          className={styles.disclaimerText}
          dangerouslySetInnerHTML={{ __html: disclaimerConfig.text }}
        />
      </div>
    </div>
  )
}
