'use client'

import type { FC } from 'react'
import React, { useEffect, useState } from 'react'
import type { IDynamicComponentProps } from '@/types/components'
import styles from '@modules/widgets/FooterDisclaimerWidget/FooterDisclaimerWidget.module.scss'

interface DisclaimerConfig {
  text: string
  responsibleGambling?: {
    message: string
    links: Array<{
      title: string
      url: string
      phone?: string
    }>
  }
}

export const FooterDisclaimerWidget: FC<IDynamicComponentProps> = ({ market }) => {
  const [disclaimerConfig, setDisclaimerConfig] = useState<DisclaimerConfig | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchDisclaimerConfig = async () => {
      try {
        // For now using mock data, later this will fetch from API
        // In production: const response = await fetch(`/api/s3/footer-config/${market}`)
        const mockDisclaimerConfig: DisclaimerConfig = {
          text: 'You understand that you are providing information to Dream Labs Entertainment LLC. The information you provide will only be used to administer this promotion. NO PURCHASE NECESSARY to enter Sweepstakes. SWEEPSTAKES ARE VOID WHERE PROHIBITED BY LAW. For detailed rules, see Sweeps Rules',
          responsibleGambling: {
            message: 'Play Responsibly',
            links: [
              {
                title: 'National Council on Problem Gambling',
                url: 'https://www.ncpgambling.org/'
              },
              {
                title: '1-800-GAMBLER',
                url: 'tel:**************',
                phone: '1-800-GAMBLER'
              },
              {
                title: 'Self-exclusion options',
                url: '/self-exclusion'
              },
              {
                title: 'Account limits/settings',
                url: '/account/limits'
              }
            ]
          }
        }
        setDisclaimerConfig(mockDisclaimerConfig)
      } catch (error) {
        console.error('Failed to fetch disclaimer config:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchDisclaimerConfig()
  }, [market])

  if (loading) {
    return <div className={styles.loading}>Loading disclaimer...</div>
  }

  if (!disclaimerConfig) {
    return null
  }

  return (
    <div className={styles.disclaimerWidget}>
      {/* Responsible Gambling Section */}
      {disclaimerConfig.responsibleGambling && (
        <div className={styles.responsibleGambling}>
          <p className={styles.rgMessage}>{disclaimerConfig.responsibleGambling.message}</p>
          <div className={styles.rgLinks}>
            {disclaimerConfig.responsibleGambling.links.map((link, index) => (
              <a
                key={index}
                href={link.url}
                target="_blank"
                rel="noopener noreferrer"
                className={styles.rgLink}
              >
                {link.phone || link.title}
              </a>
            ))}
          </div>
        </div>
      )}
      
      {/* Main Disclaimer Text */}
      <div className={styles.disclaimer}>
        <p 
          className={styles.disclaimerText}
          dangerouslySetInnerHTML={{ __html: disclaimerConfig.text }}
        />
      </div>
    </div>
  )
}
