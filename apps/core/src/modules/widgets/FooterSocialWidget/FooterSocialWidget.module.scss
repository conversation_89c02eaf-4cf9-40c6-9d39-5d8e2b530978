@use '@theme/functions.scss' as *;

.socialWidget {
  // Position this widget in the footer layout
  grid-area: social;
  display: flex;
  align-items: flex-start;
  
  // For mobile, center the social icons
  @media (max-width: 767px) {
    justify-content: center;
    margin-bottom: calculate-rem(32px);
  }
  
  // For desktop, align with logo section
  @media (min-width: 1024px) {
    justify-content: flex-start;
  }
}

.socialMedia {
  display: flex;
  gap: calculate-rem(16px);
  flex-wrap: wrap;
  align-items: center;
}

.socialLink {
  color: var(--color-text-secondary, #9ca3af);
  transition: color 0.2s ease, transform 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: calculate-rem(8px);
  border-radius: calculate-rem(4px);
  text-decoration: none;

  &:hover {
    color: var(--color-text-primary, #ffffff);
    transform: translateY(-2px);
  }

  &:focus {
    outline: 2px solid var(--color-accent, #fbbf24);
    outline-offset: 2px;
  }
  
  // Add subtle background on hover
  &:hover {
    background-color: rgba(255, 255, 255, 0.05);
  }
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: calculate-rem(20px);
  color: var(--color-text-secondary, #9ca3af);
  font-size: calculate-rem(14px);
  grid-area: social;
}

// Responsive adjustments
@media (max-width: 767px) {
  .socialMedia {
    justify-content: center;
    gap: calculate-rem(20px);
  }
  
  .socialLink {
    padding: calculate-rem(12px);
  }
}
