import type { FC } from 'react'
import React from 'react'
import { Facebook, Twitter, Twitch, MessageCircle, Send, Instagram, Youtube } from 'lucide-react'
import { getFooterConfig } from '@/network/server-utils/s3/getters'
import type { IDynamicComponentProps } from '@/types/components'
import type { FooterSocialMedia } from '@repo/types/api/s3/footer-config'
import styles from '@modules/widgets/FooterSocialWidget/FooterSocialWidget.module.scss'

const getSocialIcon = (iconName: string) => {
  const iconMap = {
    facebook: Facebook,
    twitter: Twitter,
    twitch: Twitch,
    'message-circle': MessageCircle,
    send: Send,
    instagram: Instagram,
    youtube: Youtube,
  }
  return iconMap[iconName as keyof typeof iconMap] || MessageCircle
}

export const FooterSocialWidget: FC<IDynamicComponentProps> = async ({ market }) => {
  let socialMediaItems: FooterSocialMedia[] = []

  try {
    const footerConfig = await getFooterConfig(market)
    const footerItems = footerConfig?.new_footer_items || footerConfig?.footer_items || []
    const socialMediaSection = footerItems.find(item => item.type === 'social_media')
    socialMediaItems = Array.isArray(socialMediaSection?.items) ? socialMediaSection.items as FooterSocialMedia[] : []
  } catch (error) {
    console.error('Failed to fetch footer config for social media:', error)
    return null
  }

  if (!socialMediaItems || !socialMediaItems.length) {
    return null
  }

  return (
    <div className={styles.socialWidget}>
      <div className={styles.socialMedia}>
        {socialMediaItems.map((social, index) => {
          const IconComponent = getSocialIcon(social.icon)
          return (
            <a
              key={index}
              href={social.url}
              target="_blank"
              rel="noopener noreferrer"
              className={styles.socialLink}
              aria-label={`Visit our ${social.platform} page`}
            >
              <IconComponent size={20} />
            </a>
          )
        })}
      </div>
    </div>
  )
}
