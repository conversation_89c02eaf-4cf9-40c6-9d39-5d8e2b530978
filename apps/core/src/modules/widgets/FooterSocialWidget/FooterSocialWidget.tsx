'use client'

import type { FC } from 'react'
import React, { useEffect, useState } from 'react'
import { Facebook, Twitter, Twitch, MessageCircle, Send, Instagram, Youtube } from 'lucide-react'
import type { IDynamicComponentProps } from '@/types/components'
import type { FooterSocialMedia } from '@repo/types/api/s3/footer-config'
import styles from '@modules/widgets/FooterSocialWidget/FooterSocialWidget.module.scss'

const getSocialIcon = (iconName: string) => {
  const iconMap = {
    facebook: Facebook,
    twitter: Twitter,
    twitch: Twitch,
    'message-circle': MessageCircle,
    send: Send,
    instagram: Instagram,
    youtube: Youtube,
  }
  return iconMap[iconName as keyof typeof iconMap] || MessageCircle
}

export const FooterSocialWidget: FC<IDynamicComponentProps> = ({ market }) => {
  const [socialMediaItems, setSocialMediaItems] = useState<FooterSocialMedia[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchSocialConfig = async () => {
      try {
        // For now using mock data, later this will fetch from API
        // In production: const response = await fetch(`/api/s3/footer-config/${market}`)
        const mockSocialItems: FooterSocialMedia[] = [
          { platform: 'Facebook', url: 'https://facebook.com/luckyone', icon: 'facebook' },
          { platform: 'Twitter', url: 'https://twitter.com/luckyone', icon: 'twitter' },
          { platform: 'Twitch', url: 'https://twitch.tv/luckyone', icon: 'twitch' },
          { platform: 'Discord', url: 'https://discord.gg/luckyone', icon: 'message-circle' },
          { platform: 'Telegram', url: 'https://t.me/luckyone', icon: 'send' }
        ]
        setSocialMediaItems(mockSocialItems)
      } catch (error) {
        console.error('Failed to fetch social config:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchSocialConfig()
  }, [market])

  if (loading) {
    return <div className={styles.loading}>Loading social media...</div>
  }

  if (!socialMediaItems.length) {
    return null
  }

  return (
    <div className={styles.socialWidget}>
      <div className={styles.socialMedia}>
        {socialMediaItems.map((social, index) => {
          const IconComponent = getSocialIcon(social.icon)
          return (
            <a
              key={index}
              href={social.url}
              target="_blank"
              rel="noopener noreferrer"
              className={styles.socialLink}
              aria-label={`Visit our ${social.platform} page`}
            >
              <IconComponent size={20} />
            </a>
          )
        })}
      </div>
    </div>
  )
}
