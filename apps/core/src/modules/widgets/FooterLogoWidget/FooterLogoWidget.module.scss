@use '@theme/functions.scss' as *;

.logoWidget {
  // Position this widget in the footer layout
  grid-area: logo;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  
  // For mobile-first responsive design
  margin-bottom: calculate-rem(24px);
  
  @media (min-width: 1024px) {
    margin-bottom: 0;
  }
}

.logoContainer {
  margin-bottom: calculate-rem(24px);
}

.logoLink {
  text-decoration: none;
  color: inherit;
  display: inline-block;
  transition: opacity 0.2s ease;

  &:hover {
    opacity: 0.8;
  }

  &:focus {
    outline: 2px solid var(--color-accent, #fbbf24);
    outline-offset: 2px;
    border-radius: calculate-rem(4px);
  }
}

.logoImage {
  max-height: calculate-rem(40px);
  width: auto;
  display: block;
}

.logoText {
  font-size: calculate-rem(24px);
  font-weight: 700;
  color: var(--color-accent, #fbbf24);
  letter-spacing: 0.05em;
  line-height: 1.2;
}

.logoSuperscript {
  font-size: calculate-rem(12px);
  color: var(--color-text-secondary, #9ca3af);
  margin-left: calculate-rem(4px);
  vertical-align: super;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: calculate-rem(20px);
  color: var(--color-text-secondary, #9ca3af);
  font-size: calculate-rem(14px);
}

// Responsive adjustments
@media (max-width: 767px) {
  .logoWidget {
    text-align: center;
    margin-bottom: calculate-rem(32px);
  }
}
