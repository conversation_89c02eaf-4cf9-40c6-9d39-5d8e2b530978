'use client'

import type { FC } from 'react'
import React, { useEffect, useState } from 'react'
import type { IDynamicComponentProps } from '@/types/components'
import type { GetFooterConfigLegacyResponse } from '@repo/types/api/s3/footer-config'
import styles from '@modules/widgets/FooterLogoWidget/FooterLogoWidget.module.scss'

export const FooterLogoWidget: FC<IDynamicComponentProps> = ({ market }) => {
  const [logoConfig, setLogoConfig] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchLogoConfig = async () => {
      try {
        // For now using mock data, later this will fetch from API
        // In production: const response = await fetch(`/api/s3/footer-config/${market}`)
        const mockLogoConfig = {
          name: 'LUCKY ONE',
          superscript: 'US',
          href: '/',
          imgUrl: null // Using text logo for now, can be replaced with image
        }
        setLogoConfig(mockLogoConfig)
      } catch (error) {
        console.error('Failed to fetch logo config:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchLogoConfig()
  }, [market])

  if (loading) {
    return <div className={styles.loading}>Loading logo...</div>
  }

  if (!logoConfig) {
    return null
  }

  return (
    <div className={styles.logoWidget}>
      <div className={styles.logoContainer}>
        <a 
          href={logoConfig.href || '/'} 
          className={styles.logoLink} 
          aria-label="Go to homepage"
        >
          {logoConfig.imgUrl ? (
            <img 
              src={logoConfig.imgUrl.src} 
              alt={logoConfig.imgUrl.alt || logoConfig.name}
              className={styles.logoImage}
            />
          ) : (
            <>
              <span className={styles.logoText}>{logoConfig.name}</span>
              {logoConfig.superscript && (
                <sup className={styles.logoSuperscript}>{logoConfig.superscript}</sup>
              )}
            </>
          )}
        </a>
      </div>
    </div>
  )
}
