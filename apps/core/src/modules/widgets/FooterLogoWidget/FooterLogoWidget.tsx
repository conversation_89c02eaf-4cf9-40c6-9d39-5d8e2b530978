import type { FC } from 'react'
import React from 'react'
import { getFooterConfig } from '@/network/server-utils/s3/getters'
import type { IDynamicComponentProps } from '@/types/components'
import styles from '@modules/widgets/FooterLogoWidget/FooterLogoWidget.module.scss'

interface LogoData {
  name: string
  superscript?: string
  href: string
  imgUrl?: {
    src: string
    alt: string
  } | null
}

export const FooterLogoWidget: FC<IDynamicComponentProps> = async ({ market }) => {
  let logoConfig: LogoData | null = null

  try {
    const footerConfig = await getFooterConfig(market)
    // Extract logo data from footer config or use defaults
    logoConfig = {
      name: 'LUCKY ONE',
      superscript: 'US',
      href: '/',
      imgUrl: null // Can be extracted from footerConfig if available
    }
  } catch (error) {
    console.error('Failed to fetch footer config for logo:', error)
    return null
  }

  if (!logoConfig) {
    return null
  }

  return (
    <div className={styles.logoWidget}>
      <div className={styles.logoContainer}>
        <a 
          href={logoConfig.href || '/'} 
          className={styles.logoLink} 
          aria-label="Go to homepage"
        >
          {logoConfig.imgUrl ? (
            <img 
              src={logoConfig.imgUrl.src} 
              alt={logoConfig.imgUrl.alt || logoConfig.name}
              className={styles.logoImage}
            />
          ) : (
            <>
              <span className={styles.logoText}>{logoConfig.name}</span>
              {logoConfig.superscript && (
                <sup className={styles.logoSuperscript}>{logoConfig.superscript}</sup>
              )}
            </>
          )}
        </a>
      </div>
    </div>
  )
}
