import '@/app/globals.scss'
import '@/app/globals.css'
import '@theme/style-dictionary.css'
import type { Metadata, ResolvingMetadata } from 'next'
import { serverConfig } from '@/config/serverConfig'
import { MarketProvider } from '@/context/MarketContext'
import { Footer } from '@/modules/footer/Footer'
import { getAppConfig, getWelcomePageConfig } from '@/network/server-utils/s3/getters'
import { Header } from '@components/Header'
import { SidebarLeft } from '@components/Sidebar/SidebarLeft'
import { SidebarRight } from '@components/Sidebar/SidebarRight'
import type { PageParams, PageProps } from '@repo/types/nextjs'
import { SidebarInset, SidebarProvider } from '@repo/ui/shadcn/sidebar'
import styles from '@app/[market]/layout.module.scss'

export async function generateMetadata({ params }: PageProps, _parent: ResolvingMetadata): Promise<Metadata> {
  // read route params
  const { market } = await params
  const welcomePageConfig = await getWelcomePageConfig(market)
  const appConfig = await getAppConfig(market)

  return {
    title: serverConfig.appName + ` | ${welcomePageConfig?.seo_title || market}`,
    description: welcomePageConfig?.seo_description,
  }
}

export async function generateStaticParams() {
  //  const posts = await fetch('https://.../posts').then(res => res.json())
  return serverConfig.supportedMarkets.map(market => ({ market }))
}

export const dynamicParams = false

export default async function MainLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode
  params: PageParams
}>) {
  const { market } = await params

  return (
    <div>
      <MarketProvider market={market}>
        <SidebarProvider className={styles.container}>
          <Header market={market} />
          <div className={styles.main}>
            <SidebarLeft />
            <SidebarInset>
              {children}
              <Footer />
            </SidebarInset>
            <SidebarRight />
          </div>
        </SidebarProvider>
      </MarketProvider>
    </div>
  )
}
