import 'server-only'
import { merge } from 'lodash-es'
import { unstable_cache } from 'next/cache'
import { s3FetchApi } from '@/network/server-utils/s3/S3FetchApi'

export const getAppConfig = unstable_cache(
  async (market: string) => {
    const globalConfig = await s3FetchApi.getAppConfigGlobal()
    const appConfig = await s3FetchApi.getAppConfig(market)
    return merge({}, globalConfig, appConfig) // TODO - merge the configs properly
  },
  [],
  { revalidate: 30 },
)

export const getImgIxConfig = unstable_cache(
  async () => {
    const globalConfig = await s3FetchApi.getAppConfigGlobal()
    return globalConfig?.imgIxConfig
  },
  [],
  { revalidate: 60 },
)

export const getWelcomePageConfig = async (market: string) => {
  const welcomePageConfig = await s3FetchApi.getWelcomePageConfig(market)
  return welcomePageConfig
}

export const getSidebarConfig = async (options?: RequestInit) => {
  const sidebarConfig = await s3FetchApi.getSidebarConfig(options)
  return sidebarConfig
}

export const getCasinoPageLayoutConfig = async (options?: RequestInit) => {
  const config = await s3FetchApi.getCasinoPageLayoutConfigGlobal(options)
  return config
}

export const getLiveCasinoPageLayoutConfig = async (options?: RequestInit) => {
  const config = await s3FetchApi.getLiveCasinoPageLayoutConfigGlobal(options)
  return config
}

export const getCasinoConfig = async (market: string) => {
  const config = await s3FetchApi.getCasinoConfig(market)
  return config
}

export const getFooterConfig = unstable_cache(
  async (market: string) => {
    const config = await s3FetchApi.getFooterConfig(market)
    return config
  },
  [],
  { revalidate: 60 },
)
