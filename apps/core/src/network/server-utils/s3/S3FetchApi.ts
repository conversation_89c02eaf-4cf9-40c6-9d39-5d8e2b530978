import 'server-only'
import { envVars } from '@/env'
import { BaseServerFetchApi } from '@/network/server-utils/BaseServerFetchApi'
import { s3Endpoints } from '@repo/api/endpoints/s3'
import { extractLocale } from '@repo/helpers/locale'
import type { GetCasinoConfigResponse } from '@repo/types/api/s3/casino-config'
import type { GetCasinoPageLayoutConfigGlobalResponse } from '@repo/types/api/s3/casino-page-config'
import type { GetAppConfigGlobalResponse, GetAppConfigResponse } from '@repo/types/api/s3/configs'
import type { GetSidebarConfigGlobalResponse } from '@repo/types/api/s3/sidebar-config'
import type { WelcomePageV2Response } from '@repo/types/api/s3/welcome-page-v2'
import type { IRlGame } from '@repo/types/games'

class S3<PERSON><PERSON><PERSON><PERSON><PERSON> extends BaseServerFetchApi {
  baseUrl = envVars.NEXT_PUBLIC_S3_URL
  revalidate = 15
  defaultHeaders = {
    Accept: 'application/json',
  }

  async getAllGamesList() {
    // response over 2MB cannot be cached
    return this.fetch<IRlGame[]>(s3Endpoints.gamesList, {
      next: { revalidate: 0 }, // Disable caching for large response
    })
  }

  async getAppConfigGlobal() {
    return this.fetch<GetAppConfigGlobalResponse>(s3Endpoints.appConfigGlobal)
  }

  async getAppConfig(market: string) {
    const [iso2, rlMarket] = extractLocale(market)
    return this.fetch<GetAppConfigResponse>(s3Endpoints.appConfig(iso2, rlMarket))
  }

  async getWelcomePageConfig(market: string) {
    const [iso2, rlMarket] = extractLocale(market)
    return this.fetch<WelcomePageV2Response>(s3Endpoints.welcomePage(iso2, rlMarket))
  }

  async getSidebarConfig(options?: RequestInit) {
    return this.fetch<GetSidebarConfigGlobalResponse>(s3Endpoints.sidebarConfigGlobal, options)
  }

  async getCasinoPageLayoutConfigGlobal(options?: RequestInit) {
    return this.fetch<GetCasinoPageLayoutConfigGlobalResponse>(s3Endpoints.casinoPageConfigGlobal, options)
  }

  async getLiveCasinoPageLayoutConfigGlobal(options?: RequestInit) {
    return this.fetch<GetCasinoPageLayoutConfigGlobalResponse>(s3Endpoints.liveCasinoPageConfigGlobal, options)
  }

  async getCasinoConfig(market: string) {
    const [iso2, rlMarket] = extractLocale(market)
    return this.fetch<GetCasinoConfigResponse>(s3Endpoints.casinoConfig(iso2, rlMarket))
  }

  async getLiveCasinoConfig(market: string) {
    const [iso2, rlMarket] = extractLocale(market)
    return this.fetch<GetCasinoConfigResponse>(s3Endpoints.liveCasinoConfig(iso2, rlMarket))
  }
}

export const s3FetchApi = new S3FetchApi()
