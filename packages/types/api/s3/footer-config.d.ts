export interface FooterLink {
  title: string
  url: string
  target?: '_blank' | '_self'
}

export interface FooterSection {
  title: string
  links: FooterLink[]
}

export interface FooterSocialMedia {
  platform: string
  url: string
  icon: string
}


export interface FooterLogo {
  name: string
  imgUrl: {
    alt: string
    src: string
    width: string
    device: string
    height: string
    target: string
  }
}

export interface FooterItem {
  type: 'top_section' | 'game_providers' | 'certificates' | 'social_media' | 'payment_providers' | 'disclaimer' | 'navigation_section'
  items: FooterSection[] | FooterSocialMedia[] | string | FooterLink[]
}

export interface FooterConfig {
  logo?: FooterLogo
  sections: FooterItem[]
  disclaimer?: string
  copyright?: string
}

export interface GetFooterConfigResponse extends FooterConfig {}

// Legacy support for existing footer structure
export interface LegacyFooterConfig {
  footer_items: FooterItem[]
  new_footer_items: FooterItem[]
}

export interface GetFooterConfigLegacyResponse extends LegacyFooterConfig {}
