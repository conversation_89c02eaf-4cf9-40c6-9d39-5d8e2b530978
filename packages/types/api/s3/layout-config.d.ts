/**
 * Layout Configuration System
 * 
 * This defines a reusable pattern for all layout configurations across the app.
 * Can be used for <PERSON><PERSON>, <PERSON><PERSON>, Sidebar, and any other layout components.
 */

// Base interface for all sections
export interface BaseLayoutSection {
  id: string
  enabled: boolean
  order: number
  position?: 'left' | 'center' | 'right' | 'top' | 'bottom' | 'full-width'
  responsive?: {
    mobile?: Partial<Omit<BaseLayoutSection, 'responsive'>>
    tablet?: Partial<Omit<BaseLayoutSection, 'responsive'>>
    desktop?: Partial<Omit<BaseLayoutSection, 'responsive'>>
  }
}

// Specific section types with proper typing
export interface BrandLayoutSection extends BaseLayoutSection {
  type: 'brand'
  config: BrandSectionConfig
}

export interface NavigationLayoutSection extends BaseLayoutSection {
  type: 'navigation'
  config: NavigationSectionConfig
}

export interface SocialMediaLayoutSection extends BaseLayoutSection {
  type: 'social-media'
  config: SocialMediaSectionConfig
}

export interface LegalLayoutSection extends BaseLayoutSection {
  type: 'legal'
  config: LegalSectionConfig
}

// Discriminated union - TypeScript can infer the correct type based on 'type' field
export type LayoutSection =
  | BrandLayoutSection
  | NavigationLayoutSection
  | SocialMediaLayoutSection
  | LegalLayoutSection

export interface LayoutConfig {
  id: string
  name: string
  version: string
  market: string
  lastUpdated: string
  metadata?: {
    description?: string
    author?: string
    tags?: string[]
  }
  layout: {
    type: 'grid' | 'flex' | 'stack'
    breakpoints?: {
      mobile: number
      tablet: number
      desktop: number
    }
    spacing?: {
      padding?: string
      margin?: string
      gap?: string
    }
  }
  sections: LayoutSection[]
}

// Footer-specific types extending the base layout system
export interface FooterLayoutConfig extends LayoutConfig {
  layout: LayoutConfig['layout'] & {
    type: 'flex'
    direction: 'row' | 'column'
    wrap: boolean
  }
  sections: LayoutSection[] // Use the discriminated union directly
}

// Brand Section Configuration
export interface BrandSectionConfig {
  logo: {
    type: 'text' | 'image'
    text?: string
    superscript?: string
    image?: {
      src: string
      alt: string
      width?: number
      height?: number
    }
    href: string
    target?: '_self' | '_blank'
  }
}

// Navigation Section Configuration
export interface NavigationSectionConfig {
  columns: {
    id: string
    title: string
    enabled: boolean
    links: {
      id: string
      title: string
      url: string
      target: '_self' | '_blank'
      enabled: boolean
      icon?: string
    }[]
  }[]
}

// Social Media Section Configuration
export interface SocialMediaSectionConfig {
  platforms: {
    id: string
    platform: string
    url: string
    icon: string
    enabled: boolean
    target: '_blank' | '_self'
    ariaLabel?: string
  }[]
  displayStyle: 'icons' | 'buttons' | 'text'
  size: 'small' | 'medium' | 'large'
}

// Legal Section Configuration
export interface LegalSectionConfig {
  disclaimer: {
    text: string
    enabled: boolean
  }
  responsibleGambling: {
    enabled: boolean
    message: string
    links: {
      id: string
      title: string
      url: string
      enabled: boolean
      target: '_blank' | '_self'
    }[]
  }
  companyInfo: {
    enabled: boolean
    name: string
    address: string
    license: string
    copyright: string
  }
}

// Type guards for section configs
export type FooterSectionConfig = 
  | BrandSectionConfig
  | NavigationSectionConfig  
  | SocialMediaSectionConfig
  | LegalSectionConfig

// API Response types
export interface GetFooterLayoutConfigResponse extends FooterLayoutConfig {}

// Utility types for validation
export interface LayoutConfigValidation {
  isValid: boolean
  errors: string[]
  warnings: string[]
}
