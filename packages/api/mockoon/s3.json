{"uuid": "f30c3254-5429-4487-a429-72d9d0f8643b", "lastMigration": 33, "name": "S3 API", "endpointPrefix": "", "latency": 500, "port": 8890, "hostname": "", "folders": [], "routes": [{"uuid": "712fe923-3a3b-4bb1-9e53-7226ec47c2d9", "type": "http", "documentation": "", "method": "get", "endpoint": "configs/app-config-global.json", "responses": [{"uuid": "48f64196-bd52-4ffd-a40f-3918b9bb70f3", "body": "{}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "bodyType": "FILE", "filePath": "./../data/s3/configs/app-config-global.json", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}, {"uuid": "8ce5c80e-9cb7-4ba5-963b-0f82773ab3e8", "type": "http", "documentation": "", "method": "get", "endpoint": "configs/app-config_(en|mk|fi)_([A-Z]{2,3}).json", "responses": [{"uuid": "c7a55675-026a-48e9-8863-e4e45bba770d", "body": "{}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "bodyType": "FILE", "filePath": "./../data/s3/configs/app-config_en_ROW.json", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}, {"uuid": "5d60f484-a7f6-4d73-a1af-f1d2f3c01890", "type": "http", "documentation": "", "method": "get", "endpoint": "configs/welcome-page_(en|mk|fi)_([A-Z]{2,3}).json", "responses": [{"uuid": "0da72cb6-1769-4ea5-8ae4-851cf3d56053", "body": "{}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "bodyType": "FILE", "filePath": "./../data/s3/configs/welcome-page_en_ROW.json", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}, {"uuid": "e94c875f-4e54-4724-ab01-8157b7219e9e", "type": "http", "documentation": "", "method": "get", "endpoint": "configs/welcome-page-v2_(en|mk|fi)_([A-Z]{2,3}).json", "responses": [{"uuid": "2f18c374-4255-4b2d-a285-2a0c81bc6b06", "body": "{}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "bodyType": "FILE", "filePath": "./../data/s3/configs/welcome-page-v2_en_ROW.json", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}, {"uuid": "0a1ed43b-7216-4a1b-98c2-e4fc900e58c0", "type": "http", "documentation": "", "method": "get", "endpoint": "configs/sidebar-config-global.json", "responses": [{"uuid": "476bcea7-a946-4bdd-a67c-b7d238b15e5c", "body": "{}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "bodyType": "FILE", "filePath": "./../data/s3/configs/sidebar-config-global.json", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}, {"uuid": "2ce759fe-f4c5-40d8-9927-e2ce50662441", "type": "http", "documentation": "", "method": "get", "endpoint": "configs/casino-page-config-global.json", "responses": [{"uuid": "dee6974f-79ca-45ee-bbcf-45cb79d56fd9", "body": "{}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "bodyType": "FILE", "filePath": "./../data/s3/configs/casino-page-config-global.json", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}, {"uuid": "abb2b211-70b1-4fe1-b0d5-aae65f19cf0f", "type": "http", "documentation": "", "method": "get", "endpoint": "configs/casino_(en|mk|fi)_([A-Z]{2,3}).json", "responses": [{"uuid": "c9ceec32-4821-477c-b1c8-09ecaf4e1d39", "body": "{}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "bodyType": "FILE", "filePath": "./../data/s3/lobby/casino_en_ROW.json", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}, {"uuid": "35beb2a9-437a-49be-a6a5-ac18954fae0e", "type": "http", "documentation": "", "method": "get", "endpoint": "configs/live-casino-page-config-global.json", "responses": [{"uuid": "18028477-e2dd-4812-ae80-b459b803f432", "body": "{}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "bodyType": "FILE", "filePath": "./../data/s3/configs/live-casino-page-config-global.json", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}], "rootChildren": [{"type": "route", "uuid": "712fe923-3a3b-4bb1-9e53-7226ec47c2d9"}, {"type": "route", "uuid": "5d60f484-a7f6-4d73-a1af-f1d2f3c01890"}, {"type": "route", "uuid": "8ce5c80e-9cb7-4ba5-963b-0f82773ab3e8"}, {"type": "route", "uuid": "e94c875f-4e54-4724-ab01-8157b7219e9e"}, {"type": "route", "uuid": "0a1ed43b-7216-4a1b-98c2-e4fc900e58c0"}, {"type": "route", "uuid": "2ce759fe-f4c5-40d8-9927-e2ce50662441"}, {"type": "route", "uuid": "abb2b211-70b1-4fe1-b0d5-aae65f19cf0f"}, {"type": "route", "uuid": "35beb2a9-437a-49be-a6a5-ac18954fae0e"}], "proxyMode": true, "proxyHost": "https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/casinodays", "proxyRemovePrefix": false, "tlsOptions": {"enabled": false, "type": "CERT", "pfxPath": "", "certPath": "", "keyPath": "", "caPath": "", "passphrase": ""}, "cors": true, "headers": [{"key": "Content-Type", "value": "application/json"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET,POST,PUT,PATCH,DELETE,HEAD,OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Origin, Accept, Authorization, Content-Length, X-Requested-With"}], "proxyReqHeaders": [{"key": "", "value": ""}], "proxyResHeaders": [{"key": "", "value": ""}], "data": [], "callbacks": []}