{"id": "footer-layout-en-row", "name": "<PERSON>er Layout - English ROW", "version": "1.0.0", "market": "en_ROW", "lastUpdated": "2024-12-27T10:00:00Z", "metadata": {"description": "Footer layout configuration for English Rest of World market", "author": "Frontend Team", "tags": ["footer", "layout", "en_ROW"]}, "layout": {"type": "flex", "direction": "column", "wrap": false, "breakpoints": {"mobile": 768, "tablet": 1024, "desktop": 1200}, "spacing": {"padding": "48px 0", "gap": "32px"}}, "sections": [{"id": "brand-section", "type": "brand", "enabled": true, "order": 1, "position": "left", "responsive": {"mobile": {"position": "top", "order": 1}}, "config": {"logo": {"type": "text", "text": "LUCKY ONE", "superscript": "US", "href": "/", "target": "_self"}}}, {"id": "social-media-section", "type": "social-media", "enabled": true, "order": 2, "position": "left", "responsive": {"mobile": {"position": "top", "order": 2}}, "config": {"platforms": [{"id": "facebook", "platform": "Facebook", "url": "https://facebook.com/luckyone", "icon": "facebook", "enabled": true, "target": "_blank", "ariaLabel": "Visit our Facebook page"}, {"id": "twitter", "platform": "X", "url": "https://x.com/luckyone", "icon": "twitter", "enabled": true, "target": "_blank", "ariaLabel": "Visit our X (Twitter) page"}, {"id": "twitch", "platform": "Twitch", "url": "https://twitch.tv/luckyone", "icon": "twitch", "enabled": true, "target": "_blank", "ariaLabel": "Visit our Twitch channel"}, {"id": "tiktok", "platform": "TikTok", "url": "https://tiktok.com/@luckyone", "icon": "video", "enabled": true, "target": "_blank", "ariaLabel": "Visit our TikTok page"}, {"id": "telegram", "platform": "Telegram", "url": "https://t.me/luckyone", "icon": "send", "enabled": true, "target": "_blank", "ariaLabel": "Join our Telegram channel"}], "displayStyle": "icons", "size": "medium"}}, {"id": "navigation-section", "type": "navigation", "enabled": true, "order": 3, "position": "center", "responsive": {"mobile": {"position": "full-width", "order": 3}}, "config": {"columns": [{"id": "games-column", "title": "Games", "enabled": true, "links": [{"id": "hold-win", "title": "Hold & Win", "url": "/games/hold-win", "target": "_self", "enabled": true}, {"id": "top-performing", "title": "Top performing", "url": "/games/top-performing", "target": "_self", "enabled": true}, {"id": "all-slots", "title": "All slots", "url": "/games/slots", "target": "_self", "enabled": true}, {"id": "featured", "title": "Featured", "url": "/games/featured", "target": "_self", "enabled": true}, {"id": "new-games", "title": "New", "url": "/games/new", "target": "_self", "enabled": true}]}, {"id": "other-pages-column", "title": "Other pages", "enabled": true, "links": [{"id": "promotions", "title": "Promotions", "url": "/promotions", "target": "_self", "enabled": true}, {"id": "coin-store", "title": "Coin Store", "url": "/coin-store", "target": "_self", "enabled": true}, {"id": "rg-limits", "title": "RG Limits", "url": "/responsible-gaming", "target": "_self", "enabled": true}, {"id": "leaderboard", "title": "Leaderboard", "url": "/leaderboard", "target": "_self", "enabled": true}, {"id": "jackpots", "title": "Jackpots", "url": "/jackpots", "target": "_self", "enabled": true}, {"id": "bonus-buy", "title": "Bonus buy", "url": "/bonus-buy", "target": "_self", "enabled": true}, {"id": "providers", "title": "Providers", "url": "/providers", "target": "_self", "enabled": true}, {"id": "blackjack", "title": "Blackjack", "url": "/games/blackjack", "target": "_self", "enabled": true}]}, {"id": "documents-column", "title": "Documents", "enabled": true, "links": [{"id": "terms", "title": "Terms & Conditions", "url": "/terms", "target": "_blank", "enabled": true}, {"id": "privacy", "title": "Privacy Policy", "url": "/privacy", "target": "_blank", "enabled": true}, {"id": "aml", "title": "AML policy", "url": "/aml", "target": "_blank", "enabled": true}, {"id": "fairness", "title": "Fairness", "url": "/fairness", "target": "_blank", "enabled": true}]}]}}, {"id": "legal-section", "type": "legal", "enabled": true, "order": 4, "position": "full-width", "config": {"disclaimer": {"text": "You understand that you are providing information to Dream Labs Entertainment LLC. The information you provide will only be used to administer this promotion. NO PURCHASE NECESSARY to enter Sweepstakes. SWEEPSTAKES ARE VOID WHERE PROHIBITED BY LAW. For detailed rules, see Sweeps Rules.", "enabled": true}, "responsibleGambling": {"enabled": true, "message": "Play Responsibly", "links": [{"id": "ncpg", "title": "National Council on Problem Gambling", "url": "https://www.ncpgambling.org/", "enabled": true, "target": "_blank"}, {"id": "gambler-hotline", "title": "1-800-GAMBLER", "url": "tel:**************", "enabled": true, "target": "_self"}, {"id": "self-exclusion", "title": "Self-exclusion options", "url": "/self-exclusion", "enabled": true, "target": "_self"}, {"id": "account-limits", "title": "Account limits/settings", "url": "/account/limits", "enabled": true, "target": "_self"}]}, "companyInfo": {"enabled": true, "name": "Dream Labs Entertainment LLC", "address": "571 S. Washington Ave, Afton, Wyoming 83110, United States", "license": "Licensed and regulated by Curaçao Gaming Authority", "copyright": "Copyright © 2025 luckyone.us. All rights reserved."}}}]}