class S3Endpoints {
  get appConfigGlobal() {
    return '/configs/app-config-global.json'
  }

  get gamesList() {
    return '/games/index.json'
  }

  appConfig(iso2: string, market: string) {
    return `/configs/app-config_${iso2}_${market}.json`
  }

  welcomePage(iso2: string, market: string) {
    return `/configs/welcome-page-v2_${iso2}_${market}.json`
  }

  get sidebarConfigGlobal() {
    return '/configs/sidebar-config-global.json'
  }

  get casinoPageConfigGlobal() {
    return '/configs/casino-page-config-global.json'
  }

  get liveCasinoPageConfigGlobal() {
    return '/configs/live-casino-page-config-global.json'
  }

  casinoConfig(iso2: string, market: string) {
    return `/configs/casino_${iso2}_${market}.json`
  }

  liveCasinoConfig(iso2: string, market: string) {
    return `/configs/live-casino_${iso2}_${market}.json`
  }
}

export const s3Endpoints = new S3Endpoints()
