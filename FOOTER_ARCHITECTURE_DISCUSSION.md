# [Architecture Review] Footer Layout Configuration System

## 🎯 Context & Problem

We need a configurable footer system that allows:
- Market-specific content (US vs other regions)
- Business team control without developer involvement
- Responsive design with consistent patterns
- Regulatory compliance flexibility

Based on team standup feedback, we decided against individual widget data fetching and need a layout configuration approach instead.

## 🏗️ Proposed Architecture: Layout Configuration System

Instead of individual widget data fetching, I implemented a **layout configuration approach**:

### Key Properties That Drive UI:
- **`enabled`**: Controls if section renders at all
- **`order`**: Controls visual stacking order (1, 2, 3...)  
- **`position`**: Controls which container ("left", "center", "right", "full-width")
- **`breakpoint`**: Controls responsive behavior
- **`layout.spacing.gap`**: Controls spacing between sections

### How It Works:
1. **Configuration file** defines entire footer structure
2. **LayoutConfigService** filters enabled sections and sorts by order
3. **Footer component** groups sections by position and renders
4. **CSS** uses data attributes for responsive and debugging

### Business Control:
- Marketing can enable/disable sections: `"enabled": false`
- Reorder sections: Change `"order"` values
- Move sections: Change `"position"` 
- Update content: Modify `"config"` objects
- No developer involvement needed for content changes

### Technical Benefits:
- Type-safe configuration with TypeScript interfaces
- Consistent patterns across all layout configs
- Easy A/B testing (enable/disable sections)
- Market-specific configurations (US vs international)
- Visual debugging with data attributes

## 📋 Sample Configuration Data Structure

```json
{
  "id": "footer-layout-en-row",
  "version": "1.0.0",
  "market": "US",
  "layout": {
    "type": "flex",
    "spacing": {
      "gap": "48px"
    }
  },
  "sections": [
    {
      "id": "brand-section",
      "type": "brand",
      "enabled": true,
      "order": 1,
      "position": "left",
      "config": {
        "logo": {
          "text": "LUCKY ONE",
          "superscript": "US",
          "url": "/"
        }
      }
    },
    {
      "id": "social-media-section",
      "type": "social-media", 
      "enabled": true,
      "order": 2,
      "position": "left",
      "config": {
        "platforms": [
          {"platform": "Facebook", "icon": "facebook", "url": "#", "enabled": true},
          {"platform": "X", "icon": "twitter", "url": "#", "enabled": true},
          {"platform": "Twitch", "icon": "twitch", "url": "#", "enabled": true}
        ]
      }
    },
    {
      "id": "navigation-section",
      "type": "navigation",
      "enabled": true, 
      "order": 3,
      "position": "center",
      "config": {
        "columns": [
          {
            "title": "Games",
            "links": ["Hold & Win", "Top performing", "All slots", "Featured", "New"]
          },
          {
            "title": "Other pages",
            "links": ["Promotions", "Coin Store", "RG Limits", "Leaderboard", "Jackpots"]
          },
          {
            "title": "Documents", 
            "links": ["Terms & Conditions", "Privacy Policy", "AML policy", "Fairness"]
          }
        ]
      }
    },
    {
      "id": "legal-section",
      "type": "legal",
      "enabled": true,
      "order": 4, 
      "position": "full-width",
      "config": {
        "responsibleGambling": {
          "message": "Play Responsibly",
          "links": ["National Council on Problem Gambling", "1-800-GAMBLER"]
        },
        "disclaimer": {
          "text": "You understand that you are providing information to Dream Labs Entertainment LLC..."
        },
        "companyInfo": {
          "name": "Dream Labs Entertainment LLC",
          "address": "571 S. Washington Ave, Afton, Wyoming 83110, United States",
          "license": "Licensed and regulated by Curaçao Gaming Authority"
        }
      }
    }
  ]
}
```

## 🤔 Questions for Team Review

1. **Pattern Consistency**: Does this layout configuration approach align with our existing patterns? Should other components (header, sidebar) follow this same structure?

2. **Configuration Management**: How should we handle configuration updates in production? Should these be in the database or config files?

3. **Data Fetching**: The team mentioned not using individual widget fetching - does this layout approach address that concern properly?

4. **Breakpoint Implementation**: Should the `breakpoint` property actively control CSS media queries, or just serve as documentation?

5. **Testing Strategy**: What's our preferred approach for testing configuration-driven components like this?

6. **Performance**: Any concerns about parsing configuration on each render vs caching?

## 📁 Files to Review

- `apps/core/src/components/Footer/Footer.tsx` - Main component
- `apps/core/src/services/LayoutConfigService.ts` - Configuration service  
- `apps/core/src/config/layouts/footer-layout-en-row.json` - Sample config

## 🎯 Next Steps

Looking for architectural feedback before proceeding with full implementation. Once approved, I can:

1. Complete the TypeScript interfaces
2. Add comprehensive tests
3. Create documentation
4. Implement for other markets (international, etc.)

@frontend-team @team-leads @design-system-team
